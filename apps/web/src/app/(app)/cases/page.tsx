import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import type { SearchParams } from '@lilypad/shared';
import {
  Page,
  PageBody,
  PageHeader,
  PageTitle,
} from '@lilypad/ui/components/page';
import { DataTableSkeleton } from '@lilypad/ui/data-table/data-table-skeleton';
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';
import { Suspense } from 'react';

import { casesSearchParamsCache } from '@/entities/cases/model/schema';
import { AppHeader } from '@/widgets/app-header/ui/app-header';
import { CasesTable } from '@/widgets/cases-table';

export const metadata = {
  title: 'Cases',
  description: 'Manage and view student cases',
};

interface CasesPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function CasesPage(props: CasesPageProps) {
  const searchParams = await props.searchParams;
  const search = casesSearchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters);

  // Prefetch cases data
  prefetch(
    trpc.cases.getCases.queryOptions({
      page: search.page,
      perPage: search.perPage,
      search: search.search,
      filters: validFilters,
      joinOperator: search.joinOperator,
      sort: search.sort,
    })
  );

  return (
    <Page>
      <PageHeader>
        <AppHeader>
          <PageTitle>Cases</PageTitle>
        </AppHeader>
      </PageHeader>
      <PageBody disableScroll>
        <HydrateClient>
          <Suspense
            fallback={<DataTableSkeleton columnCount={9} rowCount={10} />}
          >
            <CasesTable />
          </Suspense>
        </HydrateClient>
      </PageBody>
    </Page>
  );
}
