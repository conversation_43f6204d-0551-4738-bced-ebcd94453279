import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { routes } from '@lilypad/shared/routes';
import { DynamicBreadcrumb } from '@lilypad/ui/components/dynamic-breadcrumb';
import {
  Page,
  PageBody,
  PageHeader,
  PageSecondaryBar,
  PageTitle,
} from '@lilypad/ui/components/page';
import { Suspense } from 'react';
import { AppHeader } from '@/widgets/app-header/ui/app-header';
import {
  CaseHeaderContainerSkeleton,
  CasePageHeaderContainer,
} from '@/widgets/case-page-header';
import { CaseWorkflowContainer } from '@/widgets/case-workflow';
import { CaseWorkflowContainerSkeleton } from '@/widgets/case-workflow/ui/case-workflow-container-skeleton';

interface CasePageProps {
  params: Promise<{ id: string }>;
}

export default async function CasePage(props: CasePageProps) {
  const { id } = await props.params;

  prefetch(trpc.cases.getCaseById.queryOptions(id));

  const breadcrumbItems = [
    {
      label: 'Cases',
      href: routes.app.cases.Index,
    },
    {
      label: 'Case Details',
    },
    {
      label: `${id}`,
      copyable: true,
    },
  ];

  return (
    <Page>
      <PageHeader>
        <AppHeader>
          <PageTitle>
            <DynamicBreadcrumb items={breadcrumbItems} />
          </PageTitle>
        </AppHeader>
        <PageSecondaryBar className="h-14">
          <HydrateClient>
            <Suspense fallback={<CaseHeaderContainerSkeleton />}>
              <CasePageHeaderContainer caseId={id} />
            </Suspense>
          </HydrateClient>
        </PageSecondaryBar>
      </PageHeader>
      <PageBody>
        <HydrateClient>
          <Suspense fallback={<CaseWorkflowContainerSkeleton />}>
            <CaseWorkflowContainer caseId={id} />
          </Suspense>
        </HydrateClient>
      </PageBody>
    </Page>
  );
}
