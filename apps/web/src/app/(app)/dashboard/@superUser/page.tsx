import { HydrateClient, prefetch, trpc } from '@lilypad/api/server'
import type { SearchParams } from '@lilypad/shared';
import { Page, PageBody, PageHeader, PageTitle } from '@lilypad/ui/components/page'
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';
import { Suspense } from 'react'
import { tasksSearchParamsCache } from '@/entities/tasks/model/schema';
import { DashboardSkeleton } from '@/shared/ui/dashboard-skeleton'
import { AppHeader } from '@/widgets/app-header/ui/app-header';
import { TaskManagementContainer } from '@/widgets/task-management'

interface SuperUserDashboardPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function SuperUserDashboardPage(props: SuperUserDashboardPageProps) {
  const searchParams = await props.searchParams;
  const search = tasksSearchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters);

  prefetch(trpc.tasks.getTasks.queryOptions({
    search: search.search,
    filters: validFilters,
    joinOperator: search.joinOperator,
    sort: search.sort,
  }));
  
  prefetch(trpc.tasks.getTaskStats.queryOptions());

  return (
    <Page>
      <PageHeader>
        <AppHeader>
          <PageTitle>Dashboard</PageTitle>
        </AppHeader>
      </PageHeader>
      <PageBody disableScroll>
        <HydrateClient>
          <Suspense fallback={<DashboardSkeleton />}>
            <TaskManagementContainer />
          </Suspense>
        </HydrateClient>
      </PageBody>
    </Page>
  )
}