'use server';

import { dbAdmin } from '@lilypad/db/client';
import { districtsTable } from '@lilypad/db/schema';
import { z } from 'zod';
import { authActionClient } from '@/shared/safe-action';

export const getDistrictsAction = authActionClient
  .schema(z.object({}))
  .metadata({ actionName: 'getDistricts' })
  .action(async () => {
    const districts = await dbAdmin.transaction(async (tx) => {
      return await tx
        .select({
          id: districtsTable.id,
          name: districtsTable.name,
          type: districtsTable.type,
          website: districtsTable.website,
        })
        .from(districtsTable);
    });
    return districts;
  });
