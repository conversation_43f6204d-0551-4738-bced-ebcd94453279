'use client';

import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@lilypad/ui/components/card';
import { useState } from 'react';
import { EVENT_DEFINITIONS } from '../model/event-definitions';
import type { TriggerResult } from '../model/types';
import { EventTriggerFormCard } from './even-trigger-form-card';
import { WorkflowPhaseSelector } from './workflow-phase-selector';

export function TaskAutomationTesting() {
  const [selectedPhase, setSelectedPhase] = useState('onboarding');
  const [triggerResults, setTriggerResults] = useState<TriggerResult[]>([]);

  const phaseEvents = EVENT_DEFINITIONS.filter(
    (event) => event.phase === selectedPhase
  );

  return (
    <div className="space-y-6">
      <WorkflowPhaseSelector
        selectedPhase={selectedPhase}
        onPhaseChange={setSelectedPhase}
      />

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
        <div className="grid grid-cols-1 gap-4 md:col-span-3 md:grid-cols-2">
          {phaseEvents.map((event) => (
            <EventTriggerFormCard
              key={event.id}
              eventDefinition={event}
              onTrigger={setTriggerResults}
            />
          ))}
        </div>

        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Recent Results</CardTitle>
            </CardHeader>
            <CardContent>
              {triggerResults.length > 0 && (
                <div className="max-h-96 space-y-2 overflow-y-auto">
                  {triggerResults
                    .slice(-10)
                    .reverse()
                    .map((result, index) => (
                      <Alert key={index} className="text-xs">
                        <AlertDescription>
                          <div className="font-medium">{result.eventName}</div>
                          <div className="text-muted-foreground">
                            Event ID: {result.eventId}
                          </div>
                          <div className="text-muted-foreground">
                            {new Date(result.timestamp).toLocaleTimeString()}
                          </div>
                        </AlertDescription>
                      </Alert>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}