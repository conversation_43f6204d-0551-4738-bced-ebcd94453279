import type { RoleEnum } from '@lilypad/db/enums';
import type { Events } from '@lilypad/jobs/events';

export type WorkflowPhase =
  | 'onboarding'
  | 'pre-evaluation'
  | 'evaluation'
  | 'post-evaluation'
  | 'recurring';

export type DataFieldType =
  | 'user'
  | 'user[]'
  | 'case'
  | 'task'
  | 'date'
  | 'email[]'
  | 'text'
  | 'district';

export interface EventDefinition {
  id: string;
  name: string;
  description: string;
  phase: WorkflowPhase;
  eventType: Events;
  requiredRoles: RoleEnum[];
  requiredData: Record<string, DataFieldType>;
  prerequisites?: Events[];
  skipForTesting?: boolean;
}

export interface UserWithRole {
  id: string;
  fullName: string;
  email: string;
  roleName: RoleEnum;
}

export interface CaseWithDetails {
  id: string;
  studentName: string;
  status: string;
  priority: string;
  caseType: string;
  districtName: string;
  displayName: string;
}

export interface TriggerResult {
  success: boolean;
  eventId?: string;
  triggeredBy: string;
  timestamp: string;
  notes?: string;
  eventName: string;
  eventType: Events;
}
