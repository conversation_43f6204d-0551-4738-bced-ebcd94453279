'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useQuery } from '@tanstack/react-query';
import type { UseFormReturn } from 'react-hook-form';
import { getCasesAction } from '../api/get-cases.action';

interface CaseSelectProps {
  // biome-ignore lint/suspicious/noExplicitAny: Workaround
  form: UseFormReturn<any>;
  fieldName: string;
  label: string;
}

export function CaseSelect({ form, fieldName, label }: CaseSelectProps) {
  const { data: cases } = useQuery({
    queryKey: ['cases'],
    queryFn: () => getCasesAction({ includeInactive: false }),
    staleTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });

  const caseList = cases?.data || [];

  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <Select onValueChange={field.onChange} value={field.value}>
            <FormControl>
              <SelectTrigger className="line-clamp-1 w-full">
                <SelectValue placeholder={`Select ${label.toLowerCase()}...`} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {caseList.map((case_) => (
                <SelectItem key={case_.id} value={case_.id}>
                  {case_.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}