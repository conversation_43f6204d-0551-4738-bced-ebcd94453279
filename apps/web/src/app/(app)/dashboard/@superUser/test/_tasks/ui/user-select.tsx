'use client';

import type { RoleEnum } from '@lilypad/db/enums';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useQuery } from '@tanstack/react-query';
import type { UseFormReturn } from 'react-hook-form';
import { getUsersAction } from '../api/get-users.action';

interface UserSelectProps {
  // biome-ignore lint/suspicious/noExplicitAny: Workaround
  form: UseFormReturn<any>;
  fieldName: string;
  label: string;
  roleFilter?: RoleEnum;
}

export function UserSelect({ form, fieldName, label, roleFilter }: UserSelectProps) {
  const { data: users } = useQuery({
    queryKey: ['users', roleFilter],
    queryFn: () => getUsersAction({ roleFilter }),
    staleTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });

  const userList = users?.data || [];

  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <Select onValueChange={field.onChange} value={field.value}>
            <FormControl>
              <SelectTrigger className="line-clamp-1 w-full">
                <SelectValue placeholder={`Select ${label.toLowerCase()}...`} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {userList.map((user) => (
                <SelectItem key={user.id} value={user.id}>
                  {user.fullName} ({user.roleName})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}