'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useQuery } from '@tanstack/react-query';
import type { UseFormReturn } from 'react-hook-form';
import { getDistrictsAction } from '../api/get-districts.action';

interface DistrictSelectProps {
  // biome-ignore lint/suspicious/noExplicitAny: Workaround
  form: UseFormReturn<any>;
  fieldName: string;
  label: string;
}

export function DistrictSelect({ form, fieldName, label }: DistrictSelectProps) {
  const { data: districts } = useQuery({
    queryKey: ['districts'],
    queryFn: () => getDistrictsAction({}),
    staleTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });

  const districtList = districts?.data || [];

  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <Select onValueChange={field.onChange} value={field.value}>
            <FormControl>
              <SelectTrigger className="line-clamp-1 w-full">
                <SelectValue placeholder={`Select ${label.toLowerCase()}...`} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {districtList.map((district) => (
                <SelectItem key={district.id} value={district.id}>
                  {district.name} ({district.type})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}