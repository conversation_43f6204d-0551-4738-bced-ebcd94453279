'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useQuery } from '@tanstack/react-query';
import type { UseFormReturn } from 'react-hook-form';
import { getTasksAction } from '../api/get-tasks.action';

interface TaskSelectProps {
  // biome-ignore lint/suspicious/noExplicitAny: Workaround
  form: UseFormReturn<any>;
  fieldName: string;
  label: string;
}

export function TaskSelect({ form, fieldName, label }: TaskSelectProps) {
  const { data: tasks } = useQuery({
    queryKey: ['tasks'],
    queryFn: () => getTasksAction(),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });

  const taskList = tasks?.data || [];

  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <Select onValueChange={field.onChange} value={field.value}>
            <FormControl>
              <SelectTrigger className="line-clamp-1 w-full">
                <SelectValue placeholder={`Select ${label.toLowerCase()}...`} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {taskList.map((task) => (
                <SelectItem key={task.id} value={task.id}>
                  {task.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}