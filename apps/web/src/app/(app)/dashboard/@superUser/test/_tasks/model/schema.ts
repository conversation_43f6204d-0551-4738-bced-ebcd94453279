import { z } from 'zod';
import type { EventDefinition } from './types';

export function createEventFormSchema(eventDefinition: EventDefinition) {
  const schemaFields: Record<string, z.ZodType<unknown>> = {};

  for (const [key, type] of Object.entries(eventDefinition.requiredData)) {
    switch (type) {
      case 'user':
        schemaFields[key] = z.string().min(1, `${key} is required`);
        break;
      case 'user[]':
        schemaFields[key] = z
          .array(z.string())
          .min(1, `At least one ${key} is required`);
        break;
      case 'case':
        schemaFields[key] = z.string().min(1, `${key} is required`);
        break;
      case 'task':
        schemaFields[key] = z.string().min(1, `${key} is required`);
        break;
      case 'date':
        schemaFields[key] = z.date({ required_error: `${key} is required` });
        break;
      case 'email[]':
        schemaFields[key] = z
          .array(z.string())
          .min(1, 'At least one email is required');
        break;
      case 'text':
        schemaFields[key] = z.string().min(1, `${key} is required`);
        break;
      default:
        schemaFields[key] = z.string().min(1, `${key} is required`);
    }
  }

  return z.object(schemaFields);
}

export type EventFormData = z.infer<ReturnType<typeof createEventFormSchema>>;
