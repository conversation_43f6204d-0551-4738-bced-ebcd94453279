'use server';

import { aliasedTable, dbAdmin, eq } from '@lilypad/db/client';
import { TaskTypeEnumMap, tasksTable, usersTable } from '@lilypad/db/schema';
import { authActionClient } from '@/shared/safe-action';

export const getTasksAction = authActionClient
  .metadata({ actionName: 'getTasks' })
  .action(async () => {
    const tasks = await dbAdmin.transaction(async (tx) => {
      const assigner = aliasedTable(usersTable, 'assigner');
      const assignee = aliasedTable(usersTable, 'assignee');
      const results = await tx
        .select({
          id: tasksTable.id,
          taskType: tasksTable.taskType,
          status: tasksTable.status,
          priority: tasksTable.priority,
          assignedToName: assignee.fullName,
          assignedBy: assigner.fullName,
        })
        .from(tasksTable)
        .leftJoin(assignee, eq(assignee.id, tasksTable.assignedToId))
        .leftJoin(assigner, eq(assigner.id, tasksTable.assignedById));

      return results.map((result) => ({
        id: result.id,
        taskType: result.taskType,
        status: result.status,
        priority: result.priority,
        assignedToName: result.assignedToName || 'Unassigned',
        displayName: `${TaskTypeEnumMap[result.taskType].name} - ${result.status} (${result.assignedToName || 'Unassigned'})`,
      }));
    });

    return tasks;
  });
