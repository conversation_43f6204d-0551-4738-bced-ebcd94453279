'use client';

import { RoleEnumMap } from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { Card, CardContent, CardHeader } from '@lilypad/ui/components/card';
import { Label } from '@lilypad/ui/components/label';
import type { EventDefinition, TriggerResult } from '../model/types';
import { DynamicEventForm } from './dynamic-event-form';

interface EventTriggerFormCardProps {
  eventDefinition: EventDefinition;
  onTrigger: React.Dispatch<React.SetStateAction<TriggerResult[]>>;
}

export function EventTriggerFormCard({
  eventDefinition,
  onTrigger,
}: EventTriggerFormCardProps) {
  return (
    <Card className="flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-sm">{eventDefinition.name}</h3>
              <Badge variant="outline" className="text-xs">
                {eventDefinition.phase}
              </Badge>
            </div>
            <p className="mt-1 text-muted-foreground text-xs">
              {eventDefinition.description}
            </p>
          </div>
        </div>

        <div className="space-y-2 border-t pt-2">
          <div>
            <Label className="font-medium text-xs">Required Roles:</Label>
            <div className="mt-1 flex flex-wrap gap-1">
              {eventDefinition.requiredRoles.map((role) => (
                <Badge key={role} variant="secondary" className="text-xs">
                  {RoleEnumMap[role]}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 space-y-4">
        <DynamicEventForm
          eventDefinition={eventDefinition}
          onTrigger={onTrigger}
        />
      </CardContent>
    </Card>
  );
}