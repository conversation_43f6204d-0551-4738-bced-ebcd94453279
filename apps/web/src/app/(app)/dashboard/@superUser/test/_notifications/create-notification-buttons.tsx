'use client';

import {
  NotificationCategoryTypeEnum,
  NotificationTypeEnum,
} from '@lilypad/db/schema';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { toast } from '@lilypad/ui/components/sonner';
import { useState } from 'react';
import { createNotificationAction } from './create-notification.action';

const users = [
  {
    email: '<EMAIL>',
    name: 'Super Admin',
  },
  {
    email: '<EMAIL>',
    name: 'Special Ed Director',
  },
  {
    email: '<EMAIL>',
    name: 'School Coordinator',
  },
  {
    email: '<EMAIL>',
    name: 'School Admin',
  },
  {
    email: '<EMAIL>',
    name: '<PERSON>ctor',
  },
  {
    email: '<EMAIL>',
    name: 'Case Manager',
  },
  {
    email: '<EMAIL>',
    name: 'Clinical Director',
  },
  {
    email: '<EMAIL>',
    name: 'Psychologist',
  },
  {
    email: '<EMAIL>',
    name: 'Assistant',
  },
];

const notificationCategories = [
  {
    category: NotificationCategoryTypeEnum.GENERAL,
    label: 'General',
    description: 'Create a general notification',
    examples: [
      {
        type: NotificationTypeEnum.GENERAL,
        content:
          "👋 Welcome to the platform! We're excited to have you on board.",
      },
      {
        type: NotificationTypeEnum.SYSTEM_UPDATE,
        content:
          '🚀 New features have been added to the platform! Check out the updated dashboard.',
      },
      {
        type: NotificationTypeEnum.REMINDER,
        content: "📅 Don't forget to review your tasks for this week.",
      },
    ],
  },
  {
    category: NotificationCategoryTypeEnum.TASKS,
    label: 'Workflow',
    description: 'Create workflow-related notifications',
    examples: [
      {
        type: NotificationTypeEnum.TASK_ASSIGNED,
        content:
          "📋 A new task has been assigned to you: 'Complete student evaluation report'",
      },
      {
        type: NotificationTypeEnum.TASK_ACCEPTED,
        content:
          "✅ Your task assignment has been accepted: 'Prepare assessment materials'",
      },
      {
        type: NotificationTypeEnum.TASK_REJECTED,
        content:
          "❌ Your task assignment was rejected: 'Review district assignment' - Reason: Scheduling conflict",
      },
      {
        type: NotificationTypeEnum.EVALUATION_SCHEDULED,
        content:
          '📅 Evaluation scheduled: Student assessment for John Doe on Friday, March 15th at 10:00 AM',
      },
      {
        type: NotificationTypeEnum.RATING_SCALES_REMINDER,
        content:
          '📝 Reminder: Rating scales for student Alex Thompson are due tomorrow',
      },
      {
        type: NotificationTypeEnum.UPCOMING_EVALUATION_REMINDER,
        content:
          '📝 Reminder: Your evaluation with Sarah Johnson starts in 30 minutes (Room 205)',
      },
      {
        type: NotificationTypeEnum.MEETING_SCHEDULED,
        content:
          '👥 IEP meeting scheduled: Team meeting for student Michael Brown on Monday at 2:00 PM',
      },
      {
        type: NotificationTypeEnum.MEETING_DEADLINE_APPROACHING,
        content:
          '⏰ Deadline approaching: Your IEP meeting is scheduled for tomorrow at 2:00 PM',
      },
      {
        type: NotificationTypeEnum.APPROVAL_NEEDED,
        content:
          '✅ Your document requires approval before it can be finalized.',
      },
      {
        type: NotificationTypeEnum.REPORT_READY,
        content:
          '📄 Report ready: Evaluation report for Emma Davis is complete and ready for review',
      },
    ],
  },
];

export function CreateNotificationButtons() {
  const [selectedEmail, setSelectedEmail] = useState<string>(users[0].email);

  const handleCreateNotification = async (
    category: NotificationCategoryTypeEnum,
    type: NotificationTypeEnum,
    content: string
  ) => {
    if (!selectedEmail) {
      toast.error('Please select a user first');
      return;
    }

    try {
      const result = await createNotificationAction({
        category,
        type,
        content,
        metadata: {
          createdFrom: 'dashboard',
          timestamp: new Date().toISOString(),
        },
        email: selectedEmail,
      });

      if (result?.data?.success) {
        toast.success('Notification Created', {
          description: `Successfully sent ${category.toLowerCase()} notification to ${selectedEmail}`,
        });
      }
    } catch (_error) {
      toast.error('Failed to create notification. Please try again.');
    }
  };

  return (
    <div className="space-y-6">
      {/* User Selection */}
      <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
        <label className="shrink-0 font-medium text-sm" htmlFor="user-select">
          Send notification to:
        </label>
        <Select onValueChange={setSelectedEmail} value={selectedEmail}>
          <SelectTrigger
            className="!p-2 !h-11 w-full text-left sm:w-[280px]"
            id="user-select"
          >
            <SelectValue placeholder="Select a user" />
          </SelectTrigger>
          <SelectContent>
            {users.map((user) => (
              <SelectItem key={user.email} value={user.email}>
                <div className="flex flex-col">
                  <span className="font-medium">{user.name}</span>
                  <span className="text-muted-foreground text-xs">
                    {user.email}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Notification Categories Grid */}
      <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-3">
        {notificationCategories.map((categoryData) => (
          <Card key={categoryData.category} className="flex flex-col">
            <CardHeader className="pb-4">
              <CardTitle className="font-semibold text-xl">
                {categoryData.label}
              </CardTitle>
              <CardDescription className="text-muted-foreground text-sm">
                {categoryData.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 space-y-4">
              {categoryData.examples.map((example, index) => (
                <div key={index} className="space-y-2">
                  <p className="line-clamp-2 min-h-[2.5rem] text-muted-foreground text-sm leading-relaxed">
                    {example.content}
                  </p>
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-9 w-full font-medium text-sm"
                    onClick={() =>
                      handleCreateNotification(
                        categoryData.category,
                        example.type,
                        example.content
                      )
                    }
                  >
                    Create {example.type.replace(/_/g, ' ').toLowerCase()}
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
