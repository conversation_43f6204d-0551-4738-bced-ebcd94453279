'use server';

import { RoleEnum } from '@lilypad/db';
import type {
  BaseEmailProps,
  DistrictOnboardingEmailProps,
  FinalReportEmailProps,
  PsychologistAssignmentEmailProps,
  RatingScalesEmailProps,
} from '@lilypad/email/types';
import { inngest } from '@lilypad/jobs/client';
import { Events } from '@lilypad/jobs/events';
import { z } from 'zod';
import { authActionClient } from '@/shared/safe-action';

export const triggerInngestEventAction = authActionClient
  .schema(
    z.object({
      eventType: z.nativeEnum(Events),
      eventData: z.record(z.any()),
      notes: z.string().optional(),
    })
  )
  .metadata({ actionName: 'triggerInngestEvent' })
  .action(async ({ parsedInput, ctx }) => {
    if (!ctx.user) {
      throw new Error('User not found');
    }

    const { eventType, eventData, notes } = parsedInput;

    if (eventData.emails) {
      switch (eventType) {
        case Events.DISTRICT_ONBOARDING_COMPLETED: {
          const emails: Array<DistrictOnboardingEmailProps & BaseEmailProps> =
            eventData.emails.map((email: string) => ({
              recipientName: 'Jane <PERSON>',
              invitedByName: 'John Doe',
              invitedByEmail: '<EMAIL>',
              districtName: 'District Name',
              role: RoleEnum.SPECIAL_ED_DIRECTOR,
              schoolNames: [],
              inviteLink: 'http://localhost:3000/',
              recipient: email,
            }));
          eventData.emails = emails;
          break;
        }
        case Events.REPORT_FINAL_REVIEWED: {
          const emails: Array<FinalReportEmailProps & BaseEmailProps> =
            eventData.emails.map((email: string) => ({
              recipient: email,
              recipientName: 'Jane Smith',
              studentName: 'John Doe',
              districtName: 'District Name',
              schoolName: 'School Name',
              finalReportLink: 'http://localhost:3000/',
            }));
          eventData.emails = emails;
          break;
        }
        case Events.DISTRICT_ASSIGNMENT_ACCEPTED: {
          const emails: Array<
            PsychologistAssignmentEmailProps & BaseEmailProps
          > = eventData.emails.map((email: string) => ({
            recipient: email,
            recipientName: 'Jane Smith',
            psychologistName: 'John Doe',
            psychologistEmail: '<EMAIL>',
            psychologistAvatar: 'https://i.pravatar.cc/300',
            psychologistBio: 'John Doe is a psychologist at Lilypad Learning.',
            districtName: 'District Name',
          }));
          eventData.emails = emails;
          break;
        }
        case Events.EVALUATION_SEND_RATING_SCALES: {
          const emails: Array<RatingScalesEmailProps & BaseEmailProps> =
            eventData.emails.map((email: string) => ({
              recipient: email,
              recipientName: 'Jane Smith',
              studentName: 'John Doe',
              studentGrade: '12th Grade',
              schoolName: 'School Name',
              districtName: 'District Name',
              psychologistName: 'Psychologist Name',
              psychologistEmail: '<EMAIL>',
              ratingScales: [
                {
                  name: 'BASC-3 Teacher Rating Scale',
                  description:
                    'Assesses behavioral and emotional strengths and difficulties in school settings, including attention, learning problems, and social skills.',
                  estimatedTime: '15-20 minutes',
                  link: 'https://app.lilypadlearning.com/rating-scales/basc3-teacher',
                },
                {
                  name: 'Conners 4 Teacher Form',
                  description:
                    'Evaluates ADHD symptoms and related behavioral concerns, including hyperactivity, inattention, and executive functioning.',
                  estimatedTime: '10-15 minutes',
                  link: 'https://app.lilypadlearning.com/rating-scales/conners4-teacher',
                },
                {
                  name: 'Adaptive Behavior Assessment System',
                  description:
                    'Measures practical life skills and adaptive behaviors necessary for independence and daily functioning.',
                  estimatedTime: '20-25 minutes',
                  link: 'https://app.lilypadlearning.com/rating-scales/abas-teacher',
                },
              ],
            }));
          eventData.emails = emails;
          break;
        }
        default:
          break;
      }
    }

    const result = await inngest.send({
      name: eventType,
      data: {
        encrypted: eventData,
      },
    });

    return {
      success: true,
      eventId: result.ids?.[0],
      triggeredBy: ctx.user.id,
      timestamp: new Date().toISOString(),
      notes,
    };
  });
