import {
  AnimatedTabs,
  AnimatedTabsContent,
  AnimatedTab<PERSON><PERSON>ist,
  AnimatedTabsTrigger,
} from "@lilypad/ui/components/tabs.animated";

const WORKFLOW_PHASES = [
  { id: 'onboarding', name: 'Onboarding Phase', description: 'District setup and psychologist assignment' },
  { id: 'pre-evaluation', name: 'Pre-Evaluation Phase', description: 'Referral forms and evaluation planning' },
  { id: 'evaluation', name: 'Evaluation Phase', description: 'Assessment sessions and data collection' },
  { id: 'post-evaluation', name: 'Post-Evaluation Phase', description: 'Reports and IEP meetings' },
  { id: 'recurring', name: 'Recurring Tasks', description: 'Automated reminders and updates' },
] as const;

const workflowTabs = WORKFLOW_PHASES.map(phase => ({
  value: phase.id,
  label: phase.name,
}));

interface WorkflowPhaseSelectorProps {
  selectedPhase: string;
  onPhaseChange: (phase: string) => void;
}

export function WorkflowPhaseSelector({ selectedPhase, onPhaseChange }: WorkflowPhaseSelectorProps) {
  return (
    <AnimatedTabs
      value={selectedPhase}
      onValueChange={onPhaseChange}
      tabs={workflowTabs}
    >
      <AnimatedTabsList className="no-scrollbar" enableHorizontalScroll>
        {WORKFLOW_PHASES.map((phase) => (
          <AnimatedTabsTrigger key={phase.id} value={phase.id} className="text-xs">
            {phase.name}
          </AnimatedTabsTrigger>
        ))}
      </AnimatedTabsList>

      {WORKFLOW_PHASES.map((phase) => (
        <AnimatedTabsContent key={phase.id} value={phase.id} className="mt-4">
          <div className="rounded-lg bg-muted p-4 text-center">
            <h3 className="font-medium">{phase.name}</h3>
            <p className="mt-1 text-muted-foreground text-sm">{phase.description}</p>
          </div>
        </AnimatedTabsContent>
      ))}
    </AnimatedTabs>
  );
}