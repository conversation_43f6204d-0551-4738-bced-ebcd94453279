'use server';

import { dbAdmin, eq } from '@lilypad/db/client';
import { RoleEnum } from '@lilypad/db/enums';
import { userRolesTable, usersTable } from '@lilypad/db/schema';
import { z } from 'zod';
import { authActionClient } from '@/shared/safe-action';
import type { UserWithRole } from '../model/types';

export const getUsersAction = authActionClient
  .schema(
    z.object({
      roleFilter: z.nativeEnum(RoleEnum).optional(),
    })
  )
  .metadata({ actionName: 'getUsers' })
  .action(async ({ parsedInput }) => {
    const { roleFilter } = parsedInput;

    const users: UserWithRole[] = await dbAdmin.transaction(async (tx) => {
      const query = tx
        .select({
          id: usersTable.id,
          fullName: usersTable.fullName,
          email: usersTable.email,
          roleName: userRolesTable.roleName,
        })
        .from(usersTable)
        .innerJoin(userRolesTable, eq(userRolesTable.userId, usersTable.id));

      const results = roleFilter
        ? await query.where(eq(userRolesTable.roleName, roleFilter))
        : await query;

      return results;
    });

    return users;
  });
