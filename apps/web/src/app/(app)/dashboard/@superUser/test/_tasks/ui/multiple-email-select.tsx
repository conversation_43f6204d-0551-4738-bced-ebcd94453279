
'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { MultiSelect } from '@lilypad/ui/components/multi-select';
import { useQuery } from '@tanstack/react-query';
import type { UseFormReturn } from 'react-hook-form';
import { getUsersAction } from '../api/get-users.action';

interface MultipleEmailSelectProps {
  // biome-ignore lint/suspicious/noExplicitAny: Workaround
  form: UseFormReturn<any>;
  fieldName: string;
  label: string;
}

export function MultipleEmailSelect({ form, fieldName, label }: MultipleEmailSelectProps) {
  const { data: users } = useQuery({
    queryKey: ['users'],
    queryFn: () => getUsersAction({}),
    staleTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });

  const emailList = users?.data || [];
  const options = emailList.map((user) => ({
    label: `${user.fullName} (${user.email})`,
    value: user.email,
  }));

  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <MultiSelect
              options={options}
              selected={field.value ?? []}
              onChange={(values) => field.onChange(values)}
              placeholder={`Select ${label.toLowerCase()}...`}
              className="h-auto w-full"
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}