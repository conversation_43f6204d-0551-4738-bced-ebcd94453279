'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { RoleEnum } from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import { Form } from '@lilypad/ui/components/form';
import { toast } from '@lilypad/ui/components/sonner';
import { useForm } from 'react-hook-form';
import { triggerInngestEventAction } from '../api/trigger-inngest-event.action';
import { FIELD_LABELS } from '../model/field-mappings';
import { createEventFormSchema, type EventFormData } from '../model/schema';
import type { DataFieldType, EventDefinition, TriggerResult } from '../model/types';
import { CaseSelect } from './case-select';
import { DateSelect } from './date-select';
import { DistrictSelect } from './district-select';
import { MultipleEmailSelect } from './multiple-email-select';
import { MultipleUserSelect } from './multiple-user-select';
import { TaskSelect } from './task-select';
import { TextInput } from './text-input';
import { UserSelect } from './user-select';

interface DynamicEventFormProps {
  eventDefinition: EventDefinition;
  onTrigger: React.Dispatch<React.SetStateAction<TriggerResult[]>>;
}

export function DynamicEventForm({
  eventDefinition,
  onTrigger,
}: DynamicEventFormProps) {
  const schema = createEventFormSchema(eventDefinition);
  const form = useForm({
    resolver: zodResolver(schema),
  });

  const onSubmit = async (data: EventFormData) => {
    if (!form.formState.isValid) {
      return;
    }

    try {
      const result = await triggerInngestEventAction({
        eventType: eventDefinition.eventType,
        eventData: data,
        notes: `Testing ${eventDefinition.name}`,
      });

      if (result?.data?.success) {
        toast.success(`Event "${eventDefinition.name}" triggered successfully`);
        onTrigger((prev) => [
          ...prev,
          {
            ...(result.data as TriggerResult),
            eventName: eventDefinition.name,
            eventType: eventDefinition.eventType,
          },
        ]);
      }
    } catch (error) {
      toast.error('Failed to trigger event', {
        description: `${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  };

  const renderField = (key: string, type: DataFieldType) => {
    const label = FIELD_LABELS[key as keyof typeof FIELD_LABELS] || key;

    let roleFilter: RoleEnum | undefined;

    switch (type) {
      case 'user':
        if (key.includes('psychologist')) {
          roleFilter = RoleEnum.PSYCHOLOGIST;
        } else if (key.includes('assistant')) {
          roleFilter = RoleEnum.ASSISTANT;
        } else if (key.includes('coordinator')) {
          roleFilter = RoleEnum.SCHOOL_COORDINATOR;
        } else if (key.includes('director')) {
          roleFilter = RoleEnum.CLINICAL_DIRECTOR;
        }

        return (
          <UserSelect
            key={key}
            form={form}
            fieldName={key}
            label={label}
            roleFilter={roleFilter}
          />
        );

      case 'user[]':
        return (
          <MultipleUserSelect
            key={key}
            form={form}
            fieldName={key}
            label={label}
          />
        );

      case 'case':
        return (
          <CaseSelect
            key={key}
            form={form}
            fieldName={key}
            label={label}
          />
        );

      case 'task':
        return (
          <TaskSelect
            key={key}
            form={form}
            fieldName={key}
            label={label}
          />
        );

      case 'date':
        return (
          <DateSelect
            key={key}
            form={form}
            fieldName={key}
            label={label}
          />
        );

      case 'email[]':
        return (
          <MultipleEmailSelect
            key={key}
            form={form}
            fieldName={key}
            label={label}
          />
        );

      case 'text':
        return (
          <TextInput
            key={key}
            form={form}
            fieldName={key}
            label={label}
          />
        );

      case 'district':
        return (
          <DistrictSelect
            key={key}
            form={form}
            fieldName={key}
            label={label}
          />
        );

      default:
        return (
          <TextInput
            key={key}
            form={form}
            fieldName={key}
            label={label}
          />
        );
    }
  };

  return (
    <Form {...form}>
      <form className="space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
        {Object.entries(eventDefinition.requiredData).map(([key, type]) =>
          renderField(key, type)
        )}

        <Button
          type="submit"
          disabled={!form.formState.isValid || form.formState.isSubmitting}
          className="w-full"
          size="sm"
        >
          {form.formState.isSubmitting ? 'Triggering...' : 'Trigger Event'}
        </Button>
      </form>
    </Form>
  );
}