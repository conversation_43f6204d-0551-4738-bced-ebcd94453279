'use server';

import { and, dbAdmin, eq } from '@lilypad/db/client';
import {
  casesTable,
  districtsTable,
  schoolsTable,
  studentEnrollmentsTable,
  studentsTable,
} from '@lilypad/db/schema';
import { z } from 'zod';
import { authActionClient } from '@/shared/safe-action';
import type { CaseWithDetails } from '../model/types';

export const getCasesAction = authActionClient
  .schema(
    z.object({
      includeInactive: z.boolean().default(false),
    })
  )
  .metadata({ actionName: 'getCases' })
  .action(async ({ parsedInput }) => {
    const { includeInactive } = parsedInput;

    const baseCondition = eq(casesTable.isDeleted, false);
    const whereClause = includeInactive
      ? baseCondition
      : and(baseCondition, eq(casesTable.isActive, true));

    const cases: CaseWithDetails[] = await dbAdmin.transaction(async (tx) => {
      const results = await tx
        .select({
          id: casesTable.id,
          status: casesTable.status,
          priority: casesTable.priority,
          caseType: casesTable.caseType,
          studentName: studentsTable.fullName,
          districtName: districtsTable.name,
          isActive: casesTable.isActive,
        })
        .from(casesTable)
        .innerJoin(studentsTable, eq(studentsTable.id, casesTable.studentId))
        .innerJoin(
          studentEnrollmentsTable,
          eq(studentEnrollmentsTable.studentId, studentsTable.id)
        )
        .innerJoin(
          schoolsTable,
          eq(schoolsTable.id, studentEnrollmentsTable.schoolId)
        )
        .innerJoin(
          districtsTable,
          eq(districtsTable.id, schoolsTable.districtId)
        )
        .where(whereClause);

      return results.map((result) => ({
        id: result.id,
        studentName: result.studentName || 'Unknown',
        status: result.status,
        priority: result.priority,
        caseType: result.caseType,
        districtName: result.districtName || 'Unknown',
        displayName: `${result.studentName} - ${result.status} (${result.districtName})`,
      }));
    });

    return cases;
  });
