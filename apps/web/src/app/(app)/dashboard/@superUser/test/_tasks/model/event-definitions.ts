import { RoleEnum } from '@lilypad/db/enums';
import { Events } from '@lilypad/jobs/events';
import type { EventDefinition } from './types';

export const EVENT_DEFINITIONS: EventDefinition[] = [
  {
    id: 'district-onboarding-completed',
    name: 'District Onboarding Completed',
    description: 'Trigger when district completes onboarding process',
    phase: 'onboarding',
    eventType: Events.DISTRICT_ONBOARDING_COMPLETED,
    requiredRoles: [RoleEnum.CASE_MANAGER],
    requiredData: {
      userId: 'user',
      districtId: 'district',
      emails: 'email[]',
    },
  },
  {
    id: 'psychologist-assignment-completion',
    name: 'Psychologist Assignment Completion',
    description: 'Pairs school with psychologist that matches needs',
    phase: 'onboarding',
    eventType: Events.DISTRICT_PSYCHOLOGIST_ASSIGNED,
    requiredRoles: [RoleEnum.CASE_MANAGER, RoleEnum.PSYCHOLOGIST],
    requiredData: {
      userId: 'user',
      districtId: 'district',
      psychologistId: 'user',
    },
  },
  {
    id: 'assignment-accepted',
    name: 'Psychologist Assignment Accepted',
    description: 'Psychologist accepts school district assignment',
    phase: 'onboarding',
    eventType: Events.DISTRICT_ASSIGNMENT_ACCEPTED,
    requiredRoles: [RoleEnum.PSYCHOLOGIST, RoleEnum.SCHOOL_COORDINATOR],
    requiredData: {
      taskId: 'task',
      districtId: 'district',
      schoolCoordinatorId: 'user',
      emails: 'email[]',
    },
  },
  {
    id: 'assignment-rejected',
    name: 'Psychologist Assignment Rejected',
    description: 'Psychologist rejects school district assignment',
    phase: 'onboarding',
    eventType: Events.DISTRICT_ASSIGNMENT_REJECTED,
    requiredRoles: [RoleEnum.PSYCHOLOGIST],
    requiredData: {
      taskId: 'task',
    },
  },
  {
    id: 'referral-form-completed',
    name: 'Referral Form Completed',
    description: 'School coordinator completes referral form for students',
    phase: 'pre-evaluation',
    eventType: Events.EVALUATION_REFERRAL_FORM_COMPLETED,
    requiredRoles: [RoleEnum.SCHOOL_COORDINATOR, RoleEnum.PSYCHOLOGIST],
    requiredData: {
      taskId: 'task',
      schoolCoordinatorId: 'user',
      psychologistId: 'user',
    },
  },
  {
    id: 'evaluation-scheduling-completed',
    name: 'Evaluation Scheduling Completed',
    description: 'Schedules student evaluation and notifies team',
    phase: 'pre-evaluation',
    eventType: Events.EVALUATION_SCHEDULING_COMPLETED,
    requiredRoles: [RoleEnum.ASSISTANT],
    requiredData: {
      taskId: 'task',
      assistantId: 'user',
      teamMemberIds: 'user[]',
      evaluationDate: 'date',
    },
  },
  {
    id: 'evaluation-plan-created',
    name: 'Evaluation Plan Created',
    description: 'Reviews student info and submits evaluation plan',
    phase: 'pre-evaluation',
    eventType: Events.EVALUATION_PLAN_CREATED,
    requiredRoles: [RoleEnum.ASSISTANT],
    requiredData: {
      taskId: 'task',
      assistantId: 'user',
    },
  },
  {
    id: 'rating-scales-prepared',
    name: 'Rating Scales Prepared',
    description: 'Prepares rating scales following evaluation plan',
    phase: 'pre-evaluation',
    eventType: Events.EVALUATION_RATING_SCALES_PREPARED,
    requiredRoles: [RoleEnum.ASSISTANT],
    requiredData: {
      caseId: 'case',
      assistantId: 'user',
    },
  },
  {
    id: 'send-rating-scales',
    name: 'Send Rating Scales',
    description: 'Sends rating scales to parents and teachers',
    phase: 'pre-evaluation',
    eventType: Events.EVALUATION_SEND_RATING_SCALES,
    requiredRoles: [RoleEnum.ASSISTANT],
    requiredData: {
      caseId: 'case',
      assistantId: 'user',
      emails: 'email[]',
    },
  },
  {
    id: 'evaluation-session-start',
    name: 'Evaluation Session Started',
    description: 'Psychologist joins evaluation session',
    phase: 'evaluation',
    eventType: Events.EVALUATION_SESSION_START,
    requiredRoles: [RoleEnum.PSYCHOLOGIST],
    requiredData: {
      taskId: 'task',
      psychologistId: 'user',
    },
  },
  {
    id: 'evaluation-session-completed',
    name: 'Evaluation Session Completed',
    description: 'Marks evaluation as complete',
    phase: 'evaluation',
    eventType: Events.EVALUATION_SESSION_COMPLETED,
    requiredRoles: [RoleEnum.PSYCHOLOGIST, RoleEnum.ASSISTANT],
    requiredData: {
      taskId: 'task',
      psychologistId: 'user',
      assistantId: 'user',
    },
  },
  {
    id: 'evaluation-notes-submitted',
    name: 'Evaluation Notes Submitted',
    description: 'Submits evaluation notes and triggers report generation',
    phase: 'evaluation',
    eventType: Events.EVALUATION_NOTES_SUBMITTED,
    requiredRoles: [RoleEnum.ASSISTANT],
    requiredData: {
      taskId: 'task',
      assistantId: 'user',
    },
  },
  {
    id: 'report-draft-submitted',
    name: 'Report Draft Submitted',
    description: 'First draft of evaluation report submitted for review',
    phase: 'post-evaluation',
    eventType: Events.REPORT_DRAFT_SUBMITTED,
    requiredRoles: [RoleEnum.PSYCHOLOGIST],
    requiredData: {
      taskId: 'task',
      psychologistId: 'user',
    },
  },
  {
    id: 'report-finalized-by-psychologist',
    name: 'Report Finalized by Psychologist',
    description: 'Psychologist finalizes evaluation report',
    phase: 'post-evaluation',
    eventType: Events.REPORT_FINALIZED_BY_PSYCHOLOGIST,
    requiredRoles: [RoleEnum.PSYCHOLOGIST, RoleEnum.CLINICAL_DIRECTOR],
    requiredData: {
      taskId: 'task',
      clinicalDirectorId: 'user',
    },
  },
  {
    id: 'report-finalized-by-clinical-director',
    name: 'Report Finalized by Clinical Director',
    description: 'Clinical director finalizes and shares report with team',
    phase: 'post-evaluation',
    eventType: Events.REPORT_FINALIZED_BY_CLINICAL_DIRECTOR,
    requiredRoles: [RoleEnum.CLINICAL_DIRECTOR],
    requiredData: {
      teamMemberIds: 'user[]',
      reportUrl: 'text',
    },
  },
  {
    id: 'final-report-completed',
    name: 'Final Report Completed',
    description: 'Final report reviewed and marked as received',
    phase: 'post-evaluation',
    eventType: Events.REPORT_FINAL_REVIEWED,
    requiredRoles: [RoleEnum.SCHOOL_COORDINATOR],
    requiredData: {
      emails: 'email[]',
    },
  },
  {
    id: 'iep-meeting-scheduled',
    name: 'IEP Meeting Scheduled',
    description: 'Schedules IEP meeting and sends invitations',
    phase: 'post-evaluation',
    eventType: Events.MEETING_IEP_SCHEDULED,
    requiredRoles: [RoleEnum.ASSISTANT],
    requiredData: {
      taskId: 'task',
      assistantId: 'user',
      meetingDate: 'date',
    },
  },
  {
    id: 'iep-meeting-joined',
    name: 'IEP Meeting Joined',
    description: 'Participant joins IEP meeting',
    phase: 'post-evaluation',
    eventType: Events.MEETING_IEP_JOINED,
    requiredRoles: [RoleEnum.PSYCHOLOGIST, RoleEnum.SCHOOL_COORDINATOR],
    requiredData: {
      taskId: 'task',
      psychologistId: 'user',
    },
  },
  {
    id: 'psychologist-availability-update',
    name: 'Psychologist Availability Update Reminder',
    description: 'Reminds psychologists to update their availability',
    phase: 'recurring',
    eventType: Events.PSYCHOLOGIST_AVAILABILITY_UPDATE_REMINDER,
    requiredRoles: [RoleEnum.PSYCHOLOGIST],
    requiredData: {
      psychologistIds: 'user[]',
    },
  },
];
