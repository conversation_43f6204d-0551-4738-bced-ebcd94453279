import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@lilypad/ui/components/accordion';
import {
  Page,
  PageBody,
  PageHeader,
  PageTitle,
} from '@lilypad/ui/components/page';
import { AppHeader } from '@/widgets/app-header/ui/app-header';
import { CreateNotificationButtons } from './_notifications/create-notification-buttons';
import { TaskAutomationTesting } from './_tasks/ui/task-automation-testing';

export default function DashboardPage() {
  return (
    <Page>
      <PageHeader>
        <AppHeader>
          <PageTitle>Dashboard</PageTitle>
        </AppHeader>
      </PageHeader>
      <PageBody>
        <Accordion type="single" defaultValue="notifications" collapsible className='p-4'>
          <AccordionItem value="notifications">
            <AccordionTrigger>
              <div>
                <h2 className="font-semibold text-2xl tracking-tight">
                  Test Notification Creation
                </h2>
                <p className="text-muted-foreground">
                  Create different types of notifications to test the notification
                  system.
                </p>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <CreateNotificationButtons />
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="tasks">
            <AccordionTrigger>
              <div>
                <h2 className="font-semibold text-2xl tracking-tight">
                  Task Automation Testing
                </h2>
                <p className="text-muted-foreground">
                  Test Inngest workflow events and task automation
                </p>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <TaskAutomationTesting />
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </PageBody>
    </Page>
  );
}
