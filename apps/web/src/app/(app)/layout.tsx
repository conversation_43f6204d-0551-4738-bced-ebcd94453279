import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { SidebarInset, SidebarProvider } from '@lilypad/ui/components/sidebar';
import { getUser } from '@/entities/users/api/get-user';
import { AuthChangeListener } from '@/features/auth/lib/auth-change-listener';
import { NotificationsProvider } from '@/shared/contexts/notifications-context';
import { TransitionProvider } from '@/shared/contexts/use-transition-context';
import { UserProvider } from '@/shared/contexts/user-context';
import { AppSidebar } from '@/widgets/app-sidebar';

export default async function AppLayout({ children }: React.PropsWithChildren) {
  const user = await getUser();
  prefetch(trpc.notifications.getNotificationCounts.queryOptions());
  prefetch(trpc.notifications.getNotifications.queryOptions({}));

  return (
    <HydrateClient>
      <UserProvider initialUser={user}>
        <NotificationsProvider userId={user.id}>
          <AuthChangeListener>
            <SidebarProvider defaultOpen={false}>
              <AppSidebar user={user} />
              <SidebarInset>
                <TransitionProvider>{children}</TransitionProvider>
              </SidebarInset>
            </SidebarProvider>
          </AuthChangeListener>
        </NotificationsProvider>
      </UserProvider>
    </HydrateClient>
  );
}
