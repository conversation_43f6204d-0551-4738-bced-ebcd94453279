import type { PlanWithCase } from '@lilypad/db/repository/types/plans';
import {
  CaseP<PERSON>rityEnum,
  CaseStatusEnum,
  CaseTypeEnum,
  IepStatusEnum,
  PlanStatusEnum,
  PlanTypeEnum,
} from '@lilypad/db/schema/enums';
import { Suspense } from 'react';
import { PlanCard } from '@/entities/plans/ui/plan-card';

interface PlansPageProps {
  params: {
    id: string;
  };
}

export default async function PlansPage({ params }: PlansPageProps) {
  const plans = await getPlansByStudentId(params.id);

  return (
    <div className="p-4">
      <Suspense fallback={<div>Loading...</div>}>
        <div className="flex flex-col">
          {plans?.map((plan) => (
            <PlanCard key={plan.id} plan={plan} />
          ))}
        </div>
      </Suspense>
    </div>
  );
}

async function getPlansByStudentId(_studentId: string): Promise<PlanWithCase[]> {
  const dummyPlan: PlanWithCase = {
    id: '550e8400-e29b-41d4-a716-446655440001',
    displayId: 'plan_12348123',
    type: PlanTypeEnum.IEP,
    status: PlanStatusEnum.ACTIVE,
    expirationDate: new Date('2024-12-31'),
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-06-15'),
    case: {
      id: '550e8400-e29b-41d4-a716-446655440002',
      displayId: 'case_23203223',
      status: CaseStatusEnum.EVALUATION_IN_PROGRESS,
      priority: CasePriorityEnum.MEDIUM,
      caseType: CaseTypeEnum.INITIAL_EVALUATION,
      isActive: true,
      iepStatus: IepStatusEnum.ACTIVE,
      iepStartDate: new Date('2024-01-15'),
      iepEndDate: new Date('2024-12-31'),
      dateOfConsent: new Date('2023-12-01'),
      evaluationDueDate: new Date('2024-08-30'),
      meetingDate: new Date('2024-07-15'),
    },
  };

  return [dummyPlan];
}
