'use client';

import { useTRPC } from '@lilypad/api/client';
import { routes } from '@lilypad/shared/routes';
import { DynamicBreadcrumb } from '@lilypad/ui/components/dynamic-breadcrumb';
import { Page, PageBody, PageHeader } from '@lilypad/ui/components/page';
import { useSuspenseQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import type { PropsWithChildren } from 'react';
import { StudentAddDropdown } from '@/features/student-profile/ui/student-add-dropdown';
import { StudentProfileDetails } from '@/features/student-profile/ui/student-profile-details';
import { StudentProfilePageTabs } from '@/features/student-profile/ui/student-profile-page-tabs';
import {
  AppHeader,
  AppHeaderActions,
} from '@/widgets/app-header/ui/app-header';

interface StudentLayoutContainerProps extends PropsWithChildren {
  studentId: string;
}

export default function StudentLayoutContainer({
  children,
  studentId,
}: StudentLayoutContainerProps) {
  const trpc = useTRPC();
  const { data: student } = useSuspenseQuery(
    trpc.students.getStudentProfileById.queryOptions(studentId)
  );

  if (!student) {
    return notFound();
  }

  const breadcrumbItems = [
    {
      label: 'Students',
      href: routes.app.students.Index,
      asChild: true,
    },
    {
      label: student?.fullName || '',
    },
  ];
  return (
    <Page className="flex h-screen flex-col">
      <PageHeader className="flex-none">
        <AppHeader>
          <DynamicBreadcrumb
            items={breadcrumbItems}
            linkComponent={({ href, children: linkChildren }) => (
              <Link href={href}>{linkChildren}</Link>
            )}
          />
          <AppHeaderActions>
            <StudentAddDropdown studentId={student.id} />
          </AppHeaderActions>
        </AppHeader>
      </PageHeader>
      <PageBody
        disableScroll
        className="grid flex-1 grid-cols-1 overflow-hidden md:grid-cols-7"
      >
        <div className="overflow-y-auto border-r md:col-span-2">
          <StudentProfileDetails student={student} />
        </div>
        <div className="flex flex-col overflow-hidden md:col-span-5">
          <div className="flex-none">
            <StudentProfilePageTabs student={student} />
          </div>
          <div className="flex-1 overflow-y-auto">{children}</div>
        </div>
      </PageBody>
    </Page>
  );
}
