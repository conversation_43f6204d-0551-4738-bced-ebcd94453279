import type { AssessmentSessionSummary } from '@lilypad/db/repository/types/assessments';
import {
  AdminStatusEnum,
  CasePriorityEnum,
  CaseStatusEnum,
  CaseTypeEnum,
  IepStatusEnum,
  RoleEnum,
  SessionStatusEnum,
  SessionTypeEnum,
  TestCategoryEnum,
} from '@lilypad/db/schema/enums';
import { Suspense } from 'react';
import { AssessmentCard } from '@/entities/assessments/ui/assessment-card';

interface AssessmentsPageProps {
  params: {
    id: string;
  };
}

export default async function AssessmentsPage({
  params,
}: AssessmentsPageProps) {
  const assessments = await getAssessmentsByStudentId(params.id);

  return (
    <div className="p-4">
      <Suspense fallback={<div>Loading...</div>}>
        <div className="flex flex-col gap-4">
          {assessments?.map((assessment) => (
            <AssessmentCard key={assessment.id} assessment={assessment} />
          ))}
        </div>
      </Suspense>
    </div>
  );
}

async function getAssessmentsByStudentId(
  _id: string
): Promise<AssessmentSessionSummary[]> {
  return [
    {
      id: '01h9Qqzzz-ZZZZ-ZZZZ-ZZZZ-ZasaZZZZZZZZ',
      displayId: 'sess_29384023',
      sessionDate: new Date('2024-01-15T09:00:00Z'),
      sessionDuration: 180, // 3 hours
      sessionType: SessionTypeEnum.INITIAL_EVALUATION,
      location: 'School Psychology Office',
      sessionStatus: SessionStatusEnum.COMPLETED,
      createdAt: new Date('2024-01-10T10:00:00Z'),
      updatedAt: new Date('2024-01-15T12:00:00Z'),
      psychologist: {
        id: '01H9QZZZ-ZZZZ-ZZZZ-ZZZZ-ZZZZZZZZZZZZ',
        firstName: 'Dr. Sarah',
        lastName: 'Johnson',
        fullName: 'Dr. Sarah Johnson',
        email: '<EMAIL>',
        avatar: null,
        userRoles: [
          {
            role: {
              name: RoleEnum.PSYCHOLOGIST,
            },
          },
        ],
      },
      case: {
        id: 'case_29382032',
        status: CaseStatusEnum.EVALUATION_IN_PROGRESS,
        priority: CasePriorityEnum.HIGH,
        caseType: CaseTypeEnum.INITIAL_EVALUATION,
        iepStatus: IepStatusEnum.ACTIVE,
        evaluationDueDate: new Date('2024-02-15T00:00:00Z'),
      },
      testAdministrations: [
        {
          id: '01H9QAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA',
          administrationOrder: 1,
          adminStatus: AdminStatusEnum.COMPLETED,
          battery: {
            id: '01H9QBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB',
            name: 'Wechsler Intelligence Scale for Children, Fifth Edition',
            code: 'WISC-V',
            category: TestCategoryEnum.COGNITIVE_ASSESSMENT,
          },
        },
        {
          id: '01H9QCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC',
          administrationOrder: 2,
          adminStatus: AdminStatusEnum.COMPLETED,
          battery: {
            id: '01H9QDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD',
            name: 'Wechsler Individual Achievement Test, Fourth Edition',
            code: 'WIAT-IV',
            category: TestCategoryEnum.ACADEMIC_ACHIEVEMENT,
          },
        },
      ],
    },
  ];
}
