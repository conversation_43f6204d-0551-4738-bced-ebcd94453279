import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { createSearchParamsCache, parseAsString } from 'nuqs/server';
import { ChangeEmailSuccessCard } from '@/features/auth/ui/change-email-success-card';
import { CHANGE_EMAIL_SUCCESS_METADATA } from '../../metadata';

const paramsCache = createSearchParamsCache({
  email: parseAsString.withDefault(''),
});

export const metadata: Metadata = CHANGE_EMAIL_SUCCESS_METADATA;

export default async function ChangeEmailPage({
  searchParams
}: NextPageProps): Promise<React.JSX.Element> {
  const { email } = await paramsCache.parse(searchParams);
  if (!email) {
    return notFound();
  }
  return <ChangeEmailSuccessCard email={email} />;
}
