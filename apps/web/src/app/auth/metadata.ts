import { createMetadataTitle } from '@lilypad/shared';
import type { Metadata } from 'next';

export const AUTH_LAYOUT_METADATA: Metadata = {
  title: createMetadataTitle('Auth'),
};

export const SIGN_IN_METADATA: Metadata = {
  title: createMetadataTitle('Sign In'),
};

export const SIGN_UP_METADATA: Metadata = {
  title: createMetadataTitle('Sign Up'),
};

export const FORGOT_PASSWORD_METADATA: Metadata = {
  title: createMetadataTitle('Forgot Password'),
};

export const VERIFY_EMAIL_METADATA: Metadata = {
  title: createMetadataTitle('Verify Email'),
};

export const VERIFY_EMAIL_SUCCESS_METADATA: Metadata = {
  title: createMetadataTitle('Email Verified'),
};

export const VERIFY_EMAIL_EXPIRED_METADATA: Metadata = {
  title: createMetadataTitle('Verification Code Expired'),
};

export const RESET_PASSWORD_METADATA: Metadata = {
  title: createMetadataTitle('Reset Password'),
};

export const RESET_PASSWORD_SUCCESS_METADATA: Metadata = {
  title: createMetadataTitle('Password Reset'),
};

export const RESET_PASSWORD_EXPIRED_METADATA: Metadata = {
  title: createMetadataTitle('Password Reset Expired'),
};

export const JOIN_REQUEST_METADATA: Metadata = {
  title: createMetadataTitle('Join Request'),
};

export const TOTP_METADATA: Metadata = {
  title: createMetadataTitle('Authenticator App Setup'),
};

export const TOTP_VERIFY_METADATA: Metadata = {
  title: createMetadataTitle('Confirm via authenticator app'),
};

export const RECOVERY_CODE_METADATA: Metadata = {
  title: createMetadataTitle('Recovery Code'),
};

export const CHANGE_EMAIL_SUCCESS_METADATA: Metadata = {
  title: createMetadataTitle('Email Changed'),
};

export const CHANGE_EMAIL_EXPIRED_METADATA: Metadata = {
  title: createMetadataTitle('Change Email Expired'),
};
