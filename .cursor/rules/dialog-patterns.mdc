---
description: General rules for building dialogs
globs: apps/**/*.tsx,packages/**/*.tsx
alwaysApply: false
---
# Dialog Handling Patterns

## Overview

This rule covers three main dialog patterns used in the codebase:
1. **Simple Dialogs** - Basic form dialogs without NiceModal
2. **NiceModal Dialogs** - Programmatic modal management with NiceModal
3. **Multi-Step Dialogs** - Complex forms with stepper navigation

## Base Rules

- Use `Dialog` and `Drawer` components from `@lilypad/ui` for dialog handling
- Use `useMediaQuery` hook from `@lilypad/ui` for responsive behavior
- Use `MediaQueries` from `@lilypad/ui` for media query breakpoints
- Use `cn` from `@lilypad/ui` for class name concatenation
- Use `useState` for managing dialog state
- Use `useMutation` from TanStack Query along with tRPC endpoints for server-side handling
- Use Sonner for toast notifications and UI feedback

## Common Dialog Structure

### Responsive Pattern
```typescript
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';

const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

// Always use conditional rendering for responsive behavior
{mdUp ? (
  <Dialog>
    <DialogContent>...</DialogContent>
  </Dialog>
) : (
  <Drawer>
    <DrawerContent>...</DrawerContent>
  </Drawer>
)}
```

### Standard Layout Structure
```typescript
<DialogContent className="flex max-w-2xl flex-col gap-0 p-0">
  <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
    <DialogTitle className="font-semibold text-lg">Title</DialogTitle>
    <DialogDescription className="text-muted-foreground text-sm">
      Description
    </DialogDescription>
  </DialogHeader>
  
  <div className="min-h-0 flex-1 overflow-y-auto px-6 py-4">
    {/* Form content */}
  </div>
  
  <div className="flex-shrink-0 bg-secondary p-4">
    {/* Action buttons */}
  </div>
</DialogContent>
```

## Simple Dialog Pattern

### Basic Structure
```typescript
'use client';

import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';

interface SimpleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SimpleDialog({ open, onOpenChange }: SimpleDialogProps) {
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  const renderForm = (
    <div className={cn(
      'min-h-0 flex-1 overflow-y-auto',
      mdUp ? 'px-6 py-4' : 'p-4'
    )}>
      <form className="space-y-6 pb-4">
        {/* Form fields */}
      </form>
    </div>
  );

  const renderButtons = (
    <div className={cn(
      'flex flex-shrink-0 justify-between gap-2 bg-secondary p-4',
      mdUp && 'rounded-b-md'
    )}>
      <Button
        type="button"
        variant="outline"
        size="sm"
        className="w-1/2 md:w-auto"
        onClick={() => onOpenChange(false)}
      >
        Cancel
      </Button>
      <Button
        type="submit"
        variant="default"
        size="sm"
        className="w-1/2 md:w-auto"
      >
        Submit
      </Button>
    </div>
  );

  return (
    <>
      {mdUp ? (
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent className="flex max-w-2xl flex-col gap-0 p-0">
            <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
              <DialogTitle className="font-semibold text-lg">
                Dialog Title
              </DialogTitle>
              <DialogDescription className="text-muted-foreground text-sm">
                Dialog description
              </DialogDescription>
            </DialogHeader>
            {renderForm}
            {renderButtons}
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer open={open} onOpenChange={onOpenChange}>
          <DrawerContent className="flex flex-col">
            <DrawerHeader className="flex-shrink-0 border-b text-left">
              <DrawerTitle className="font-semibold text-lg">
                Dialog Title
              </DrawerTitle>
              <DrawerDescription className="text-muted-foreground text-sm">
                Dialog description
              </DrawerDescription>
            </DrawerHeader>
            {renderForm}
            {renderButtons}
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
}
```

## NiceModal Pattern

### Enhanced Modal Hook
```typescript
import { useEnhancedModal } from '@/shared/hooks/use-enhanced-modal';

// Always use useEnhancedModal for NiceModal components
const modal = useEnhancedModal();
```

### Basic NiceModal Structure
```typescript
'use client';

import NiceModal, { type NiceModalHocProps } from '@ebay/nice-modal-react';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { useEnhancedModal } from '@/shared/hooks/use-enhanced-modal';

export type NiceModalProps = NiceModalHocProps & {
  // Add your specific props here
  data: string;
};

export const NiceModalComponent = NiceModal.create<NiceModalProps>(({ data }) => {
  const modal = useEnhancedModal();
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  const renderForm = (
    <div className={cn(
      'min-h-0 flex-1 overflow-y-auto',
      mdUp ? 'px-6 py-4' : 'p-4'
    )}>
      {/* Form content */}
    </div>
  );

  const renderButtons = (
    <div className={cn(
      'flex flex-shrink-0 gap-2 bg-secondary p-4 md:justify-between md:rounded-b-md'
    )}>
      <Button
        type="button"
        variant="outline"
        size="sm"
        className="w-1/2 md:w-auto"
        onClick={modal.handleClose}
      >
        Cancel
      </Button>
      <Button
        type="button"
        variant="default"
        size="sm"
        className="w-1/2 md:w-auto"
        onClick={modal.handleClose}
      >
        Submit
      </Button>
    </div>
  );

  return (
    <>
      {mdUp ? (
        <Dialog open={modal.visible} onOpenChange={modal.handleOpenChange}>
          <DialogContent
            className="flex max-w-lg flex-col gap-0 p-0"
            onAnimationEndCapture={modal.handleAnimationEndCapture}
          >
            <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
              <DialogTitle>Modal Title</DialogTitle>
              <DialogDescription className="sr-only">
                Modal description
              </DialogDescription>
            </DialogHeader>
            {renderForm}
            {renderButtons}
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer open={modal.visible} onOpenChange={modal.handleOpenChange}>
          <DrawerContent className="flex flex-col">
            <DrawerHeader className="flex-shrink-0 border-b text-left">
              <DrawerTitle>Modal Title</DrawerTitle>
              <DrawerDescription className="sr-only">
                Modal description
              </DrawerDescription>
            </DrawerHeader>
            {renderForm}
            {renderButtons}
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
});
```

### Alert Dialog with NiceModal
```typescript
export const AlertModal = NiceModal.create<AlertModalProps>(({ factorId }) => {
  const modal = useEnhancedModal();
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  const handleSubmit = async () => {
    // Handle submission
    modal.handleClose();
  };

  const renderButtons = (
    <>
      <Button type="button" variant="outline" onClick={modal.handleClose}>
        Cancel
      </Button>
      <Button type="button" variant="destructive" onClick={handleSubmit}>
        Confirm Action
      </Button>
    </>
  );

  return (
    <>
      {mdUp ? (
        <AlertDialog open={modal.visible} onOpenChange={modal.handleOpenChange}>
          <AlertDialogContent
            className="max-w-sm"
            onAnimationEndCapture={modal.handleAnimationEndCapture}
          >
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Action</AlertDialogTitle>
              <AlertDialogDescription>Are you sure?</AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>{renderButtons}</AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      ) : (
        <Drawer open={modal.visible} onOpenChange={modal.handleOpenChange}>
          <DrawerContent>
            <DrawerHeader className="text-left">
              <DrawerTitle>Confirm Action</DrawerTitle>
              <DrawerDescription>Are you sure?</DrawerDescription>
            </DrawerHeader>
            <DrawerFooter className="flex-col-reverse pt-4">
              {renderButtons}
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
});
```

### NiceModal Usage Pattern
```typescript
// Show modal programmatically
const handleShowModal = async () => {
  const result = await NiceModal.show(MyModal, {
    data: 'some data',
  });
  
  if (result) {
    // Handle result
  }
};

// Show modal with chaining
const handleShowChainedModals = async () => {
  const firstResult = await NiceModal.show(FirstModal, { data: 'first' });
  if (firstResult) {
    const secondResult = await NiceModal.show(SecondModal, { data: 'second' });
    // Handle second result
  }
};
```

## Multi-Step Dialog Pattern

### Step Definition
```typescript
type Step = {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
};

const STEPS: Step[] = [
  {
    id: 'step1',
    title: 'Step 1',
    description: 'First step description',
    icon: <Icon1 className="size-4" />,
  },
  {
    id: 'step2',
    title: 'Step 2',
    description: 'Second step description',
    icon: <Icon2 className="size-4" />,
  },
];

const { Stepper, useStepper } = defineStepper(...STEPS);
```

### Multi-Step Dialog Structure
```typescript
function MultiStepDialogContent({ onClose }: { onClose: () => void }) {
  const stepperMethods = useStepper();
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  const canNavigateToStep = (stepId: string) => {
    // Add your navigation logic here
    return true;
  };

  const handleStepClick = (stepId: string) => {
    if (canNavigateToStep(stepId)) {
      stepperMethods.goTo(stepId);
    }
  };

  const isCurrentStepComplete = () => {
    // Add your completion logic here
    return true;
  };

  const buttonLabel = useButtonLabel(stepperMethods, 'Submit');

  return (
    <DialogContent
      className="flex h-[90vh] max-h-[90vh] min-w-5xl flex-col gap-0 p-0"
      onCloseAutoFocus={() => null}
      onEscapeKeyDown={(e) => e.preventDefault()}
      onInteractOutside={(e) => e.preventDefault()}
    >
      <DialogDescription className="sr-only">
        Multi-step dialog
      </DialogDescription>
      <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
        <DialogTitle>Multi-Step Dialog</DialogTitle>
      </DialogHeader>

      <div className="flex min-h-0 flex-1 gap-6 border-b px-6">
        {mdUp && (
          <div className="w-1/4 flex-shrink-0 border-r">
            <Stepper.Navigation className="py-6">
              {stepperMethods.all.map((step) => (
                <Stepper.Step
                  disabled={!canNavigateToStep(step.id)}
                  icon={step.icon}
                  key={step.id}
                  of={step.id}
                  onClick={() => handleStepClick(step.id)}
                >
                  <Stepper.Title className="font-medium text-sm">
                    {step.title}
                  </Stepper.Title>
                  <Stepper.Description className="text-muted-foreground text-xs">
                    {step.description}
                  </Stepper.Description>
                </Stepper.Step>
              ))}
            </Stepper.Navigation>
          </div>
        )}

        <div className="min-h-0 flex-1 overflow-hidden py-4">
          <Stepper.Panel className="h-full overflow-y-auto">
            {/* Render current step content */}
          </Stepper.Panel>
        </div>
      </div>

      <div className="flex-shrink-0 rounded-b-xl bg-secondary p-4">
        <Stepper.Controls className="flex justify-between">
          <Button size="sm" variant="cancel" onClick={onClose}>
            Cancel
          </Button>
          <div className="flex gap-2">
            {!stepperMethods.isFirst && (
              <Button
                onClick={stepperMethods.prev}
                size="sm"
                type="button"
                variant="outline"
              >
                Previous
              </Button>
            )}
            <Button
              disabled={!isCurrentStepComplete()}
              onClick={stepperMethods.isLast ? onClose : stepperMethods.next}
              size="sm"
            >
              {buttonLabel}
              {!stepperMethods.isLast && <ArrowRightIcon className="size-4" />}
            </Button>
          </div>
        </Stepper.Controls>
      </div>
    </DialogContent>
  );
}
```

### Multi-Step Dialog Container
```typescript
export function MultiStepDialog({ open, onOpenChange }: MultiStepDialogProps) {
  const [resetKey, setResetKey] = useState(0);
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleReset = () => {
    setResetKey((prev) => prev + 1);
  };

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      handleReset();
    }
    onOpenChange(isOpen);
  };

  return (
    <>
      {mdUp ? (
        <Dialog open={open} onOpenChange={handleOpenChange}>
          <Stepper.Provider variant="vertical">
            <MultiStepDialogContent key={resetKey} onClose={handleClose} />
          </Stepper.Provider>
        </Dialog>
      ) : (
        <Drawer open={open} onOpenChange={handleOpenChange}>
          <Stepper.Provider variant="vertical">
            <MultiStepDialogContent key={resetKey} onClose={handleClose} />
          </Stepper.Provider>
        </Drawer>
      )}
    </>
  );
}
```

## Form Integration Patterns

### Form with React Hook Form
```typescript
import { Form } from '@lilypad/ui/components/form';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';

export function FormDialog({ open, onOpenChange }: FormDialogProps) {
  const methods = useZodForm({
    schema: formSchema,
    defaultValues: {
      // Default values
    },
  });

  const onSubmit = async (data: FormData) => {
    // Handle form submission
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <Form {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)}>
            {/* Form fields */}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
```

### Form with tRPC Mutation
```typescript
const { mutate: submitForm, isPending } = useMutation(
  trpc.endpoint.mutate.mutationOptions({
    onSuccess: () => {
      toast.success('Success!');
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`Error: ${error.message}`);
    },
  })
);

const onSubmit = (data: FormData) => {
  submitForm(data);
};
```

## Accessibility Patterns

### Focus Management
```typescript
// Prevent focus trap issues
<DialogContent
  onCloseAutoFocus={() => null}
  onEscapeKeyDown={(e) => e.preventDefault()}
  onInteractOutside={(e) => e.preventDefault()}
>
```

### Screen Reader Support
```typescript
<DialogDescription className="sr-only">
  Dialog description for screen readers
</DialogDescription>
```

### Loading States
```typescript
<Button
  disabled={isPending}
  loading={isPending}
  onClick={handleSubmit}
>
  {isPending ? 'Saving...' : 'Save'}
</Button>
```

## Best Practices

### State Management
- Use `useState` for simple open/close state
- Use NiceModal for programmatic modal management
- Reset form state when dialog closes

### Performance
- Use `key` prop for resetting stepper state
- Implement proper loading states
- Avoid unnecessary re-renders

### Error Handling
- Handle form validation errors
- Show appropriate error messages
- Implement proper error boundaries

### Responsive Design
- Always use `useMediaQuery` for responsive behavior
- Test on both desktop and mobile
- Ensure proper touch targets on mobile

### Responsive Design
- Always use `useMediaQuery` for responsive behavior
- Test on both desktop and mobile
- Ensure proper touch targets on mobile
