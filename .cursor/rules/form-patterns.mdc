---
description: General rules for building forms with React Hook Form, Zod, and Shadcn UI
globs: apps/**/*.tsx,packages/**/*.tsx
alwaysApply: false
---
# Form Handling Patterns

## Base Rules

- Use React Hook Form for form validation and submission
- Use Zod for form validation schemas
- Use `useZodForm` hook from `@lilypad/shared/hooks/use-zod-form` for form setup
- Use `useMutation` from TanStack Query along with tRPC endpoints for server-side handling
- Use Sonner for toast notifications and UI feedback
- Always use `@lilypad/ui` components for form UI

## Hook Pattern - useZodForm

### Implementation
```typescript
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';

const methods = useZodForm({
  schema: formSchema,
  defaultValues: getDefaultValues(data),
  mode: 'onSubmit', // or 'onChange', 'onBlur'
});
```

### Benefits
- Automatic Zod resolver integration with `raw: true` option
- Type-safe form handling
- Consistent validation patterns across the app
- Built-in error handling

## Form Component Structure

### Container Pattern
```typescript
export function FormContainer() {
  const { methods, canSubmit, onSubmit, isSaving } = useFormHook();
  
  return (
    <Form {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>
        <FormContent methods={methods} isSaving={isSaving} />
        <FormActions canSubmit={canSubmit} />
      </form>
    </Form>
  );
}
```

### Field Pattern
```typescript
<FormField
  control={methods.control}
  name="fieldName"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Field Label</FormLabel>
      <FormDescription>Optional description</FormDescription>
      <FormControl>
        <Input
          placeholder="Placeholder text"
          disabled={isSubmitting}
          {...field}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

## Mutation Integration

### tRPC Mutation Pattern
```typescript
const { mutate: submitForm, isPending: isSaving } = useMutation(
  trpc.endpoint.mutate.mutationOptions({
    onSuccess: (data) => {
      toast.success('Operation completed successfully');
      queryClient.invalidateQueries({
        queryKey: [trpc.endpoint.queryKey],
      });
      // Additional success handling
    },
    onError: (error: TRPCClientError) => {
      const message = error.message || 'An unexpected error occurred';
      toast.error(`Operation failed: ${message}`);
      // Additional error handling
    },
  })
);
```

### Submit Handler
```typescript
const onSubmit = useCallback(
  (data: FormData) => {
    if (!canSubmit) return;
    submitForm(data);
  },
  [canSubmit, submitForm]
);

const canSubmit = !isSaving && (!methods.formState.isSubmitted || methods.formState.isDirty);
```

## Schema Definition Patterns

### Entity Schema Location
```
app/web/src/entities/[entity]/model/schema.ts    # Reusable entity schemas
app/web/src/features/[feature]/model/schemas.ts  # Feature-specific form schemas
packages/api/src/schemas/[entity].ts # tRPC input/output schemas
```

### Schema Structure
```typescript
import { z } from 'zod';

export const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  preferences: z.array(z.object({
    type: z.string(),
    enabled: z.boolean(),
  })),
}).refine((data) => {
  // Custom validation logic
  return data.preferences.some(p => p.enabled);
}, {
  message: 'At least one preference must be enabled',
  path: ['preferences'],
});

export type FormData = z.infer<typeof formSchema>;
```

## Error Handling Patterns

### Field-Level Errors
```typescript
// Zod schema provides automatic field validation
<FormMessage /> // Displays field-specific errors
```

### Form-Level Errors
```typescript
// Handle form submission errors
onError: (error: TRPCClientError) => {
  toast.error(`Failed to save: ${error.message}`);
}
```

### Loading States
```typescript
<Button disabled={!canSubmit} type="submit">
  {isSaving ? 'Saving...' : 'Save'}
</Button>
```

## Advanced Patterns

### Dynamic Field Arrays
```typescript
const { fields, append, remove } = useFieldArray({
  control: methods.control,
  name: 'items',
});

{fields.map((field, index) => (
  <FormField
    key={field.id}
    control={methods.control}
    name={`items.${index}.value`}
    render={({ field }) => (
      // Field implementation
    )}
  />
))}
```

### Conditional Fields
```typescript
const watchedValue = methods.watch('conditionalField');

{watchedValue && (
  <FormField
    control={methods.control}
    name="dependentField"
    // Field implementation
  />
)}
```

### Master Toggle Pattern
```typescript
const masterEnabled = methods.watch('masterToggle');

<FormField
  control={methods.control}
  name="childField"
  render={({ field }) => (
    <FormItem>
      <FormControl>
        <Switch
          checked={field.value && masterEnabled}
          disabled={!masterEnabled || isSubmitting}
          onCheckedChange={field.onChange}
        />
      </FormControl>
    </FormItem>
  )}
/>
```

## Form Hook Pattern

### Standard Form Hook Structure
```typescript
export function useFormHook() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const methods = useZodForm({
    schema: formSchema,
    mode: 'onSubmit',
    defaultValues: {
      // Default values
    },
  });

  const canSubmit = !isSubmitting && !methods.formState.isSubmitting;

  const onSubmit = useCallback(
    async (data: FormData) => {
      if (!canSubmit) return;
      
      setIsSubmitting(true);
      try {
        // Handle submission
        await submitData(data);
        toast.success('Success!');
      } catch (error) {
        toast.error('Failed to submit');
      } finally {
        setIsSubmitting(false);
      }
    },
    [canSubmit]
  );

  return {
    methods,
    canSubmit,
    onSubmit,
    isSaving: isSubmitting,
  };
}
```

## Accessibility Requirements

### Form Labels
- Always use `<FormLabel>` for field labels
- Provide meaningful `aria-label` attributes for complex controls
- Use `<FormDescription>` for helpful context

### Error Messages
- Use `<FormMessage>` for field-level errors
- Ensure error messages are descriptive and actionable
- Test with screen readers

### Keyboard Navigation
- Ensure all form controls are keyboard accessible
- Test tab order and focus management
- Provide keyboard shortcuts where appropriate

## Performance Considerations

### Form Optimization
- Use `defaultValues` to prevent unnecessary re-renders
- Implement proper form state management
- Avoid inline functions in form field definitions

### Validation Optimization
- Use Zod for efficient client-side validation
- Implement debounced validation for expensive operations
- Consider async validation for server-side checks

### Validation Optimization
- Use Zod for efficient client-side validation
- Implement debounced validation for expensive operations
- Consider async validation for server-side checks