---
type: "always_apply"
---

# Turborepo Structure and Package Management Rules

## Workspace Overview

This is a Turborepo monorepo with the following structure:

```
apps/
├── web/          # Main Next.js application (FSD architecture)
└── docs/         # Documentation site

packages/
├── analytics/    # Analytics tracking
├── api/          # tRPC integration, API routes and schemas
├── core/         # Contains Service layer classes containing core, server-side business logic
├── db/           # Database schemas and functions (Dr<PERSON>zle)
├── editor/       # Rich text editor components
├── email/        # Email templates and service
├── jobs/         # Background job processing with Inngest
├── kv/           # Key-value store and rate limiting with Upstash
├── monitoring/   # Application monitoring with Sentry
├── payments/     # Billing and payment handling with Stripe
├── scripts/      # Database seeding and utilities
├── shared/       # Globally shared utilities and types
├── supabase/     # Supabase client and auth utilities
└──  ui/           # Shared UI components (Shadcn/UI)

supabase/
├── migrations/   # Generated migration files -- should not manually create or update migration files
├── templates/    # Supabase Auth-specific email templates
├── config.toml   # Supabase configuration file
└── seed.sql      # Custom SQL seed logic

tooling/
├── typescript/   # Shared TypeScript configurations
├── tailwindcss/  # Shared Tailwind configuration
└── requirements/ # Development requirements

turbo/
└── generators/   # Template creationg and scaffolding scripts
```

## Package Import Rules

### Import Hierarchy
Apps can import from any package, but packages should have minimal cross-dependencies:

```typescript
// ✅ Apps importing packages
import { Button } from '@lilypad/ui/components'
import { db } from '@lilypad/db/client'
import { getSupabaseServerClient } from '@lilypad/supabase/server'

// ✅ Packages importing shared utilities
import { logger } from '@lilypad/shared/logger'
import { routes } from '@lilypad/shared/routes'

// ❌ Avoid package-to-package dependencies unless necessary
// In @lilypad/ui/components package
import { db } from '@lilypad/db/client' // Generally avoid this
```

### Package-Specific Import Patterns

#### UI Package (`@lilypad/ui`)
```typescript
// ✅ Import UI components in apps
import { Button, Input, Dialog } from '@repo/ui/components'
import { cn } from '@lilypad/ui/lib/utils'
import { toast } from '@lilypad/ui/components/sonner';

// Component export pattern in packages/ui/src/components/button.tsx
export { Button } from './button'
```

#### Database Package (`@lilypad/db`)
```typescript
// ✅ Import database utilities
import { createDatabaseClient, sql } from '@lilypad/db/client';
import { users, organizations } from '@lilypad/db/schema'
import { createUser, getUser } from '@lilypad/db/functions'

// Schema organization in packages/db/src/schema/
export * from './users'
export * from './organizations'
export * from './contacts'
```

#### Supabase Package (`@lilypad/supabase`)
```typescript
// ✅ Import Supabase utilities
import { getSupabaseServerClient } from '@lilypad/supabase/server'
import { getCurrentUser } from '@lilypad/supabase/auth'
import { uploadFile } from '@lilypad/supabase/storage'
```

## Package Development Rules

### When Creating New Packages

1. **Add to workspace**: Update `pnpm-workspace.yaml`
2. **Package structure**:
```
packages/new-package/
├── package.json
├── tsconfig.json
├── src/
│   ├── index.ts        # Main exports
│   └── [feature-files]
└── README.md
```

3. **Package.json template**:
```json
{
  "name": "@lilypad/package-name",
  "version": "0.0.0",
  "type": "module",
  "exports": {
    ".": {
      "types": "./src/index.ts",
      "default": "./src/index.ts"
    }
  },
  "dependencies": {},
  "devDependencies": {
    "@lilypad/typescript": "workspace:*"
  }
}
```

### Package Responsibility Guidelines

#### UI Package (`@lilypad/ui`)
- **Purpose**: Globally shared component library based on Shadcn/UI
- **Contents**: 
  - Reusable UI components
  - Shared hooks for UI interactions
  - Design system utilities
  - Tailwind configuration
- **Rules**:
  - Components must be framework-agnostic where possible
  - Use Tailwind classes, avoid custom CSS unless necessary and use Shadcn class naming standards
  - Export both individual components and barrel exports
  - Include TypeScript definitions for all props

#### Database Package (`@lilypad/db`)
- **Purpose**: Database schema, migrations, and data access layer
- **Contents**:
  - Drizzle schema definitions
  - Database connection configuration
  - Reusable database functions
  - Migration files
- **Rules**:
  - All database operations should go through this package
  - Use transactions for complex operations
  - Export typed schema objects for use in apps
  - Keep database functions pure and testable

#### Shared Package (`@lilypad/shared`)
- **Purpose**: Common utilities, types, and configurations
- **Contents**:
  - Utility functions
  - Common TypeScript types
  - Error handling utilities
  - Logging configuration
  - Route definitions
- **Rules**:
  - Must be framework-agnostic
  - No external dependencies unless absolutely necessary
  - All exports must be typed
  - Functions should be pure where possible

### Cross-Package Communication

#### Database Operations
```typescript
// ✅ In apps/web/src/features/user/model/get-user.ts
import { db } from '@lilypad/db/client'
import { users } from '@lilypad/db/schema'
import { eq } from 'drizzle-orm'

export async function getUser(id: string) {
  return db.select().from(users).where(eq(users.id, id))
}
```

#### UI Component Usage
```typescript
// ✅ In apps/web/src/features/auth/ui/sign-in-form.tsx
import { Button,  Form, FormField  } from '@lilypad/ui/components'
import { toast } from '@lilypad/ui/components/sonner';
```

#### Shared Utilities
```typescript
// ✅ In any package or app
import { logger } from '@lilypad/shared/logger'
import { HttpError } from '@lilypad/shared/errors'
import { routes } from '@lilypad/shared/routes'
```

## Build and Development Rules

### Turbo Pipeline Configuration
Reference `turbo.json` for build dependencies:

```json
{
  "build": {
    "dependsOn": ["^build"],
    "outputs": [".next/**", "!.next/cache/**"]
  },
  "dev": {
    "cache": false,
    "persistent": true
  }
}
```

### Development Workflow
1. **Installing dependencies**: Use `pnpm add` at workspace root
2. **Running dev servers**: `pnpm dev` for all apps, or `pnpm dev --filter=web`
3. **Building packages**: Packages build automatically when referenced
4. **Adding package dependencies**: `pnpm add @lilypad/package-name --filter=app-name`

### Package Versioning
- Use `workspace:*` for internal package dependencies
- Bump versions together for related packages
- Use semantic versioning for external packages

## Code Organization Within Packages

### UI Package Organization
```
packages/ui/src/
├── components/       # Base Shadcn components
├── composite/        # Complex composed components
├── hooks/            # Reusable hooks
├── icons/            # Custom icon components
├── lib/              # Utilities (cn, etc.)
├── styles/           # Global styles
└── index.ts          # Main exports
```
### Database Package Organization
```
packages/db/src/
├── schema/           # Drizzle schema definitions
│   ├── auth.ts
│   ├── enums.ts
│   ├── index.ts
│   ├── tables.ts
│   └── types.ts
├── policies/         # RLS policies and policy helpers
├── repository/       # Reusable database repository classes organized by domain
├── config/           # Database connection config
└── index.ts          # Main exports
```

## Anti-Patterns to Avoid

### ❌ Circular Dependencies
```typescript
// DON'T: Package A importing from Package B that imports from Package A
// @lilypad/ui importing from @lilypad/db
import { getUserPreferences } from '@lilypad/db'
```

### ❌ App-Specific Code in Packages
```typescript
// DON'T: App-specific business logic in shared packages
// In @lilypad/ui/src/components/user-profile.tsx
import { getUser } from '../../../apps/web/src/features/user'
```

### ❌ Direct Database Access in UI Package
```typescript
// DON'T: Database operations in UI components
// In @lilypad/ui/src/components/user-list.tsx
import { db } from '@lilypad/db/client'
```

## Migration and Refactoring Guidelines

### Moving Code Between Packages
1. **Identify dependencies** and update imports
2. **Update package.json** dependencies
3. **Run type checking** across workspace
4. **Update documentation** and examples

### Creating New Shared Components
1. **Start in app**: Develop component in specific app first
2. **Identify reusability**: Determine if component is truly reusable
3. **Generalize**: Remove app-specific logic
4. **Move to package**: Transfer to appropriate shared package
5. **Update imports**: Replace app imports with package imports

### Package Splitting Guidelines
Split packages when:
- Package becomes too large (>50 files) EXCEPT for the packages/ui package
- Clear domain boundaries exist
- Different deployment requirements
- Different dependency requirements

Keep packages together when:
- Tight coupling between features
- Shared types and utilities
- Similar update frequencies
