---
type: "always_apply"
---

## Architecture Overview

This project follows Feature-Sliced Design (FSD) methodology with the following layer hierarchy:

```
app/ → widgets/ → features/ → entities/ → shared/
```

**Import Rule**: Higher layers can import from lower layers, but NEVER the reverse.

API routes and implementations have been extracted into a dedicated @lilypad/api package in the Turborepo. No /api directories remain within feature or entity layers.

## Layer Definitions & Responsibilities

### 1. App Layer (`src/app/`)
**Purpose**: Application-level configuration, routing, and global providers
**Responsibilities**:
- Next.js App Router pages and layouts
- Global providers and configurations
- API routes
- Root-level error boundaries
- SEO configuration (robots, sitemap, manifest)

**Rules**:
- Can import from ALL lower layers (widgets, features, entities, shared)
- Contains only routing logic, global configs, and top-level compositions
- No business logic - delegate API calls to `@lilypad/api` functions imported within widget or feature model layers
- Each route should primarily compose widgets and features
- You can prefetch data using tRPC routes from `@lilypad/api` in layout.tsx or page.tsx server components if necessary

### 2. Widgets Layer (`src/widgets/`)
**Purpose**: Complex UI blocks that compose features and entities
**Responsibilities**:
- Reusable compound components
- Layout components (sidebars, navigation)
- Complex forms that combine multiple features
- Command palettes and modals

**Rules**:
- Can import from: features, entities, shared (NOT from app)
- Each widget should have its own folder with `ui/` subfolder and `index.ts`
- Widgets compose features and entities, don't contain business logic
- Focus on UI composition and user interaction flows

### 3. Features Layer (`src/features/`)
**Purpose**: User scenarios and business logic implementation
**Responsibilities**:
- Complete user workflows (auth, contact-management, onboarding)
- Business logic and API interactions
- Form handling and validation
- State management for specific features

**Rules**:
- Can import from: entities, shared, and @lilypad/api (NOT from app, widgets)
- Structure: `feature-name/ui/`, `feature-name/model/`, `feature-name/lib/`,
- No `api/` subfolder—API calls must reference functions from `@lilypad/api`
- `model/` contains business logic, Zod schemas, hooks
- `lib/` contains feature-specific utilities, constants, and helpers
- `ui/` contains feature-specific components
- Each feature should be self-contained and reusable

### 4. Entities Layer (`src/entities/`)
**Purpose**: Business entities and their core operations
**Responsibilities**:
- Data models and types
- CRUD operations for specific entities
- Entity-specific UI components
- Data fetching and caching logic

**Rules**:
- Can import from: shared and `@lilypad/api` (NOT from app, widgets, features)
- Structure: `entity-name/ui/`, `entity-name/model/`, `entity-name/lib/`
- No `api/` subfolder—actual server calls live in `@lilypad/api`
- `model/` contains core entity logic, transformations, and Zod schemas
- `lib/` contains entity-specific utilities
- `ui/` contains entity-specific display components
- Should be pure and reusable across different features

### 5. Shared Layer (`src/shared/`)
**Purpose**: Reusable utilities, components, and configurations
**Responsibilities**:
- Generic UI components
- Utility functions
- Configuration files
- Common hooks and types
- API clients and error handling

**Rules**:
- Cannot import from any other layer
- Must be completely framework-agnostic and reusable
- Organize by technical purpose: `ui/`, `lib/`, `hooks/`, `config/`, `types/`
- Components should be generic and configurable

## File Organization Rules

### Directory Structure
```
layer-name/
├── ui/                 # React components
├── api/                # API calls and server actions
├── model/              # Business logic and state
├── lib/                # Utilities and helpers
└── index.ts            # Public API exports
```

### Naming Conventions
- **Folders**: kebab-case (`contact-management`, `auth-forms`)
- **Files**: kebab-case (`add-contact-button.tsx`, `sign-in-with-credentials.ts`)
- **Components**: PascalCase (`AddContactButton`, `SignInForm`)
- **Functions**: camelCase (`addContact`, `signInWithCredentials`)

### Export Patterns
- Each layer folder must have an `index.ts` that exports public API
- Use named exports, avoid default exports except for React components
- Re-export from subfolders to create clean import paths

## Import Rules & Enforcement

### Valid Import Patterns
```typescript
// ✅ App layer imports
import { AppSidebar } from '@/widgets/app-sidebar'
import { StudentManagement } from '@/features/student-management'
import { Student } from '@/entities/students'
import { Button } from '@/shared/ui/buttons'

// ✅ Widget layer imports
import { AddStudent } from '@/features/student-management'
import { StudentsList } from '@/entities/contact'
import { Dialog } from '@/shared/ui/modals'

// ✅ Feature layer imports
import { getStudents } from '@/entities/students'
import { actionClient } from '@/shared/api/safe-action'

// ✅ Entity layer imports
import { formatDate } from '@/shared/lib/formatters'

// ✅ Shared layer imports
import { cn } from '@/shared/lib/utils'
```

### Forbidden Import Patterns
```typescript
// ❌ Lower layers importing from higher layers
// In entities/students/model/get-students.ts
import { StudentFilters } from '@/features/student-management' // FORBIDDEN

// In shared/ui/button.tsx
import { useAuth } from '@/features/auth' // FORBIDDEN

// ❌ Circular imports within same layer
// In features/auth/ui/sign-in-form.tsx
import { SignUpForm } from '@/features/auth/ui/sign-up-form' // BAD DESIGN
```

## Component Creation Rules

### When Creating UI Components

1. **Determine the correct layer**:
   - Generic, reusable → `shared/ui/`
   - Entity-specific display → `entities/[entity]/ui/`
   - Feature-specific workflow → `features/[feature]/ui/`
   - Complex composition → `widgets/[widget]/ui/`
   - Page-level composition → `app/`

2. **Component structure**:
```typescript
// Feature component example
// features/contact-management/ui/add-contact-modal.tsx
import { Dialog } from '@/shared/ui/modals'
import { addStudent } from '../model/add-student'

export function AddStudentModal({ onClose }: Props) {
  // Component logic using feature's model
}
```

### When Creating Business Logic

1. **Always place in appropriate model/ folder**:
   - Pure entity operations → `entities/[entity]/model/`
   - Complex workflows → `features/[feature]/model/`

2. **Use tRPC pattern for server component prefetching**:
```typescript
// src/app/(app)/students/[id]/layout.tsx
import { Suspense } from 'react';

import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import StudentLayoutContainer from './layout.container';
import StudentLayoutSkeleton from './layout.skeleton';

interface StudentLayoutProps {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}

export default async function StudentLayout({
  children,
  params,
}: StudentLayoutProps) {
  const { id } = await params;

  prefetch(trpc.students.getStudentProfileById.queryOptions(id));

  return (
    <HydrateClient>
      <Suspense fallback={<StudentLayoutSkeleton />}>
        <StudentLayoutContainer studentId={id}>
          {children}
        </StudentLayoutContainer>
      </Suspense>
    </HydrateClient>
  );
}
```

## Page Creation Rules

### App Router Pages
- Keep pages minimal - they should primarily compose widgets and features
- Business logic belongs in features, not pages
- Use layout.tsx for shared UI patterns

```typescript
// app/students/page.tsx
import { StudentManagement } from '@/widgets/student-management'
import { AppSidebar } from '@/widgets/app-sidebar'

export default function StudentsPage() {
  return <StudentManagement /> // Delegate to widget
}
```

## State Management Rules

1. **Local component state**: Use React useState/useReducer
2. **Feature state**: Keep in feature's model layer
3. **Global state**: Use Zustand in app layer or shared hooks
4. **Server state**: Use appropriate data fetching from `@liypad/api`

## Error Handling Rules

1. **API errors**: Handle in model layer where API calls are made
2. **UI errors**: Use error boundaries at appropriate levels
3. **Form errors**: Handle in feature's UI components
4. **Global errors**: Handle in app layer

## Common Anti-Patterns to Avoid
### ❌ Wrong Layer Placement
```typescript
// DON'T: Business logic in shared layer
// shared/lib/add-student.ts - WRONG!

// DON'T: Generic component in feature layer
// features/auth/ui/button.tsx - WRONG!
```

### ❌ Import Violations
```typescript
// DON'T: Entity importing from feature
import { StudentFilters } from '@/features/student-management'

// DON'T: Shared importing from entity
import { Student } from '@/entities/students'
```

### ❌ Tight Coupling
```typescript
// DON'T: Direct feature-to-feature dependencies
// features/auth/model/sign-in.ts
import { addStudent } from '@/features/student-management'
```

## Code Generation Guidelines

### When generating new code:

1. **Always ask**: Which layer does this belong to?
2. **Check imports**: Ensure no upward dependencies
3. **Follow structure**: Use ui/, model/, lib/ organization
4. **Keep focused**: Each piece should have a single responsibility
5. **Make it reusable**: Avoid hardcoding specific implementations

### File templates to use:

#### Feature Model File:
```typescript
// features/[feature]/model/[action].ts
import { actionClient } from '@/shared/api/safe-action'
import { schema } from '@/shared/types/schemas'

export const actionName = actionClient
  .metadata({ actionName: "actionName" })
  .schema(schema)
  .action(async ({ parsedInput }) => {
    // Implementation
  })
```

#### Entity Model File:
```typescript
// entities/[entity]/model/[operation].ts
import { cache } from '@/shared/api/caching'

export const getEntity = cache(
  async (params: Params) => {
    // Data fetching logic
  },
  ['entity-key']
)
```

#### Widget Component:
```typescript
// widgets/[widget]/ui/[component].tsx
import { Feature } from '@/features/[feature]'
import { Entity } from '@/entities/[entity]'
import { SharedComponent } from '@/shared/ui/[component]'

export function Widget() {
  // Composition logic
}
```

## Validation Checklist

Before generating or modifying code, verify:

- [ ] File is in the correct layer
- [ ] Imports only come from lower layers
- [ ] Business logic is in model/ folders
- [ ] UI components are in ui/ folders
- [ ] Utilities are in lib/ folders
- [ ] Each folder has proper index.ts exports
- [ ] No circular dependencies
- [ ] Single responsibility principle followed
- [ ] Appropriate abstraction level for the layer
- [ ] API data fetching is from `@lilypad/api` 

## Migration Guidelines

When refactoring existing code:

1. **Identify the correct layer** for each piece of functionality
2. **Move business logic** from UI components to model files
3. **Extract reusable components** to appropriate shared locations
4. **Fix import violations** by restructuring dependencies
5. **Update exports** to maintain clean public APIs

Follow these rules strictly to maintain clean architecture and prevent technical debt.
