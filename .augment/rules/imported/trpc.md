---
type: "always_apply"
---

# tRPC API Usage Rules - @lilypad/api Package

## Architecture Overview

This project uses tRPC with TanStack React Query integration for all client-side data fetching and mutations, centralizing setup and enforcing type-safe hook usage.

**Package Structure**:
```
packages/api/src/
├── core/
│   ├── client.tsx      # Client-side tRPC setup
│   ├── server.tsx      # Server-side tRPC setup
│   ├── init.ts         # tRPC initialization and procedures
│   └── query-client.ts # TanStack Query client configuration
├── routers/
│   ├── _app.ts         # Main app router
│   ├── students.ts     # Domain-specific routers
│   └── [other].ts      # Other domain routers
└── schemas/
    ├── students.ts     # Zod schemas for validation
    └── [other].ts      # Other domain schemas
```

## Import Patterns

### Client-Side Imports (React Components)
```typescript
// ✅ Client-side tRPC usage
import { useTRPC } from '@lilypad/api/client';
import type { TRPCClientError } from '@lilypad/api/client';

// ✅ Schema imports
import { studentSchema } from '@lilypad/api/schemas/students';
```

### Server-Side Imports (Server Components, Layouts)
```typescript
// ✅ Server-side tRPC usage
import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { caller } from '@lilypad/api/server'; // For direct server calls
```

### Provider Setup
```typescript
// ✅ App-level provider setup
import { TRPCReactProvider } from '@lilypad/api/client';

// In app layouts or providers
<TRPCReactProvider>
  {children}
</TRPCReactProvider>
```

## Client-Side Usage Patterns

### Query Usage with useSuspenseQuery
```typescript
// ✅ Standard query pattern
function StudentsTable() {
  const trpc = useTRPC();
  const searchParams = useSearchParams();
  
  const search = React.useMemo(() => {
    const params = Object.fromEntries(searchParams.entries());
    return studentsSearchParamsCache.parse(params);
  }, [searchParams]);

  const { data, isError } = useSuspenseQuery(
    trpc.students.getStudents.queryOptions(
      {
        page: search.page,
        perPage: search.perPage,
        search: search.search,
        filters: validFilters,
        joinOperator: search.joinOperator,
        sort: search.sort,
      },
      {
        staleTime: 1000,
      }
    )
  );

  // Handle error state
  if (isError) {
    return <ErrorComponent />;
  }

  // Use data
  return <DataComponent data={data} />;
}
```

### Mutation Usage with Custom Hooks
```typescript
// ✅ Custom mutation hook pattern
function useBulkCreateStudents(options: UseBulkCreateStudentsOptions = {}) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const handleSuccess = useCallback(
    (results: StudentSaveResult[]) => {
      // Invalidate related queries
      void queryClient.invalidateQueries({
        queryKey: [['students']],
        exact: false,
      });

      options.onSuccess?.(results);
    },
    [queryClient, options]
  );

  const handleError = useCallback(
    (error: TRPCClientError) => {
      const message = error.message || 'An unexpected error occurred';
      toast.error(`Failed to create students: ${message}`);
      options.onError?.(error);
    },
    [options]
  );

  const {
    mutate,
    mutateAsync,
    isPending: isSaving,
    error,
    data,
  } = useMutation(
    trpc.students.bulkCreateStudents.mutationOptions({
      onSuccess: handleSuccess,
      onError: handleError,
    })
  );

  return {
    bulkCreateStudents: mutate,
    bulkCreateStudentsAsync: mutateAsync,
    isSaving,
    error,
    data,
  };
}
```

### Error Handling Pattern
```typescript
// ✅ Proper error handling
import type { TRPCClientError } from '@lilypad/api/client';

const handleError = useCallback(
  (error: TRPCClientError) => {
    const message = error.message || 'An unexpected error occurred';
    toast.error(`Operation failed: ${message}`);
    
    // Optional: Handle specific error codes
    if (error.data?.code === 'UNAUTHORIZED') {
      // Handle auth errors
    }
    
    options.onError?.(error);
  },
  [options]
);
```

## Server-Side Usage Patterns

### Data Prefetching in Layouts
```typescript
// ✅ Prefetch data in server components
import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';

export default async function StudentLayout({
  children,
  params,
}: StudentLayoutProps) {
  const { id } = await params;

  // Prefetch data on the server
  prefetch(trpc.students.getStudentProfileById.queryOptions(id));

  return (
    <HydrateClient>
      <Suspense fallback={<StudentLayoutSkeleton />}>
        <StudentLayoutContainer studentId={id}>
          {children}
        </StudentLayoutContainer>
      </Suspense>
    </HydrateClient>
  );
}
```

### Multiple Prefetch Pattern
```typescript
// ✅ Multiple prefetch operations
export default async function StudentsPage() {
  // Prefetch multiple queries
  prefetch(trpc.students.getStudents.queryOptions({ page: 1 }));
  prefetch(trpc.districts.getDistricts.queryOptions());
  prefetch(trpc.schools.getSchools.queryOptions());

  return (
    <HydrateClient>
      <StudentsPageContent />
    </HydrateClient>
  );
}
```

### Direct Server Calls (When Needed)
```typescript
// ✅ Direct server calls (use sparingly)
import { caller } from '@lilypad/api/server';

export async function generateStaticParams() {
  const students = await caller.students.getStudents({ page: 1 });
  return students.data.map(student => ({ id: student.id }));
}
```

## Router Development Patterns

### Router Structure
```typescript
// ✅ Router definition pattern
// packages/api/src/routers/students.ts
import { authenticatedProcedure, createTRPCRouter } from '../core/init';
import { getStudentsInputSchema, bulkStudentSchema } from '../schemas/students';

export const studentsRouter = createTRPCRouter({
  // Query procedures
  getStudents: authenticatedProcedure
    .input(getStudentsInputSchema)
    .query(async ({ ctx, input }): Promise<GetStudentsResult> => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new StudentRepository(tx);
        return await repository.getStudents(input as GetStudentsParams);
      });
    }),

  // Single item query
  getStudentById: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new StudentRepository(tx);
        return await repository.getStudentById(input);
      });
    }),

  // Mutation procedures
  bulkCreateStudents: authenticatedProcedure
    .input(bulkStudentSchema)
    .mutation(async ({ ctx, input }): Promise<StudentSaveResult[]> => {
      return await ctx.db.transaction(async (tx) => {
        const service = new StudentsService(tx);
        return await service.bulkCreateStudents(input, ctx.user);
      });
    }),
});
```

### Adding New Routers
```typescript
// ✅ Adding new router to app router
// packages/api/src/routers/_app.ts
import { createTRPCRouter } from '../core/init';
import { studentsRouter } from './students';
import { newDomainRouter } from './new-domain';

export const appRouter = createTRPCRouter({
  students: studentsRouter,
  newDomain: newDomainRouter, // Add new domain router
});

export type AppRouter = typeof appRouter;
```

## Schema Development Patterns

### Zod Schema Organization
```typescript
// ✅ Schema definition pattern
// packages/api/src/schemas/students.ts
import { z } from 'zod';
import { GenderEnum, SchoolGradeEnum } from '@lilypad/db/enums';

// Input schemas for procedures
export const getStudentsInputSchema = z.object({
  page: z.number().min(1).optional(),
  perPage: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  filters: z.array(z.object({
    id: z.string(),
    value: z.unknown(),
    isMulti: z.boolean().optional(),
  })).optional(),
});

// Complex nested schemas
export const studentSchema = z.object({
  id: z.string(),
  studentIdNumber: z.string().min(1, 'Student ID is required'),
  firstName: z.string().min(1, 'First name is required'),
  grade: z.nativeEnum(SchoolGradeEnum),
  parents: z.array(parentSchema).min(1, 'At least one parent is required'),
}).refine((data) => {
  // Custom validation logic
  return data.parents.some(parent => parent.isPrimaryContact);
}, {
  message: 'At least one parent must be marked as primary contact',
  path: ['parents'],
});

// Type exports
export type Student = z.infer<typeof studentSchema>;
export type GetStudentsInput = z.infer<typeof getStudentsInputSchema>;
```

### Schema Export Pattern
```typescript
// ✅ Package.json exports for schemas
{
  "exports": {
    "./server": "./src/core/server.tsx",
    "./client": "./src/core/client.tsx",
    "./schemas/students": "./src/schemas/students.ts",
    "./schemas/districts": "./src/schemas/districts.ts"
  }
}
```

## Authentication & Authorization

### Procedure Types
```typescript
// ✅ Use appropriate procedure types
import { baseProcedure, authenticatedProcedure } from '../core/init';

export const publicRouter = createTRPCRouter({
  // Public endpoints (rare)
  getPublicData: baseProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      // No auth required
    }),

  // Most endpoints should be authenticated
  getProtectedData: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      // ctx.user is available and typed
      const userId = ctx.user.id;
    }),
});
```

### Context Usage
```typescript
// ✅ Using context in procedures
authenticatedProcedure
  .input(bulkStudentSchema)
  .mutation(async ({ ctx, input }) => {
    // Database transaction
    return await ctx.db.transaction(async (tx) => {
      const service = new StudentsService(tx);
      // User context available
      return await service.bulkCreateStudents(input, ctx.user);
    });
  });
```

## Performance Optimization

### Query Optimization
```typescript
// ✅ Efficient query usage
const { data } = useSuspenseQuery(
  trpc.students.getStudents.queryOptions(
    searchParams,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10,   // 10 minutes
    }
  )
);
```

### Cache Invalidation
```typescript
// ✅ Proper cache invalidation
const handleSuccess = useCallback(
  (results: StudentSaveResult[]) => {
    // Invalidate specific queries
    void queryClient.invalidateQueries({
      queryKey: [['students']],
      exact: false,
    });

    // Or invalidate specific query
    void queryClient.invalidateQueries({
      queryKey: [['students', 'getStudents']],
    });
  },
  [queryClient]
);
```

### Optimistic Updates
```typescript
// ✅ Optimistic updates for better UX
const { mutate } = useMutation(
  trpc.students.updateStudent.mutationOptions({
    onMutate: async (newStudent) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['students'] });

      // Snapshot previous value
      const previousStudents = queryClient.getQueryData(['students']);

      // Optimistically update
      queryClient.setQueryData(['students'], (old) => {
        // Update logic
      });

      return { previousStudents };
    },
    onError: (err, newStudent, context) => {
      // Rollback on error
      queryClient.setQueryData(['students'], context?.previousStudents);
    },
    onSettled: () => {
      // Always refetch after error or success
      void queryClient.invalidateQueries({ queryKey: ['students'] });
    },
  })
);
```

## Error Handling Rules

### Client-Side Error Handling
```typescript
// ✅ Comprehensive error handling
import type { TRPCClientError } from '@lilypad/api/client';

const handleError = useCallback(
  (error: TRPCClientError) => {
    // Generic error message
    const message = error.message || 'An unexpected error occurred';
    
    // Handle specific error codes
    switch (error.data?.code) {
      case 'UNAUTHORIZED':
        toast.error('Please sign in to continue');
        // Redirect to login
        break;
      case 'FORBIDDEN':
        toast.error('You do not have permission to perform this action');
        break;
      case 'NOT_FOUND':
        toast.error('The requested resource was not found');
        break;
      case 'TOO_MANY_REQUESTS':
        toast.error('Too many requests. Please try again later');
        break;
      default:
        toast.error(`Operation failed: ${message}`);
    }
    
    // Call custom error handler
    options.onError?.(error);
  },
  [options]
);
```

### Server-Side Error Handling
```typescript
// ✅ Server-side error handling in procedures
import { TRPCError } from '@trpc/server';

authenticatedProcedure
  .input(z.string())
  .query(async ({ ctx, input }) => {
    try {
      return await ctx.db.transaction(async (tx) => {
        const repository = new StudentRepository(tx);
        const student = await repository.getStudentById(input);
        
        if (!student) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Student not found',
          });
        }
        
        return student;
      });
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch student',
        cause: error,
      });
    }
  });
```


## Common Anti-Patterns to Avoid

### ❌ Wrong Import Patterns
```typescript
// DON'T: Import from internal paths
import { appRouter } from '@lilypad/api/src/routers/_app';

// DON'T: Import server utilities in client components
import { caller } from '@lilypad/api/server'; // In client component

// DON'T: Import client utilities in server components
import { useTRPC } from '@lilypad/api/client'; // In server component
```

### ❌ Incorrect Query Usage
```typescript
// DON'T: Use regular useQuery with tRPC
import { useQuery } from '@tanstack/react-query';

function BadComponent() {
  const { data } = useQuery({
    queryKey: ['students'],
    queryFn: () => trpc.students.getStudents.query(),
  });
}

// DO: Use tRPC's query options
function GoodComponent() {
  const trpc = useTRPC();
  const { data } = useSuspenseQuery(
    trpc.students.getStudents.queryOptions(params)
  );
}
```

### ❌ Missing Error Handling
```typescript
// DON'T: Ignore error states
function BadComponent() {
  const { data } = useSuspenseQuery(
    trpc.students.getStudents.queryOptions()
  );
  
  // No error handling
  return <div>{data.map(...)}</div>;
}

// DO: Handle errors properly
function GoodComponent() {
  const { data, isError } = useSuspenseQuery(
    trpc.students.getStudents.queryOptions()
  );
  
  if (isError) {
    return <ErrorComponent />;
  }
  
  return <div>{data.map(...)}</div>;
}
```

### ❌ Incorrect Mutation Usage
```typescript
// DON'T: Use mutation directly without proper setup
function BadComponent() {
  const trpc = useTRPC();
  
  const handleSubmit = async (data) => {
    await trpc.students.bulkCreateStudents.mutate(data);
  };
}

// DO: Use proper mutation pattern
function GoodComponent() {
  const { bulkCreateStudents, isSaving } = useBulkCreateStudents({
    onSuccess: (results) => {
      toast.success('Students created successfully');
    },
    onError: (error) => {
      // Error handling is in the hook
    },
  });
  
  const handleSubmit = (data) => {
    bulkCreateStudents(data);
  };
}
```

## File Organization Rules

### Router Files
- Place in `packages/api/src/routers/`
- Name after domain (e.g., `students.ts`, `districts.ts`)
- Export single router with domain name
- Import and add to `_app.ts`

### Schema Files
- Place in `packages/api/src/schemas/`
- Name after domain (e.g., `students.ts`, `districts.ts`)
- Export schemas and types
- Add to package.json exports if used by apps

### Core Files
- Don't modify `core/` files unless adding new procedures or middleware
- Follow existing patterns for authentication and context

## Migration Guidelines

### Adding New Procedures
1. **Define schema** in appropriate schema file
2. **Create procedure** in domain router
3. **Add to app router** if new domain
4. **Update package.json exports** if schema is used by apps

### Updating Existing Procedures
1. **Update schema** with backward compatibility
2. **Update procedure** implementation
3. **Update client usage** where needed
4. **Test** all affected components

### Deprecating Procedures
1. **Mark as deprecated** in JSDoc comments
2. **Add migration guide** in comments
3. **Update client usage** to new procedures
4. **Remove after migration period**

## Validation Checklist

Before implementing tRPC changes:

- [ ] Correct import pattern used (`@lilypad/api/client` vs `@lilypad/api/server`)
- [ ] Proper authentication procedure used (`authenticatedProcedure` vs `baseProcedure`)
- [ ] Input validation with Zod schemas
- [ ] Error handling implemented
- [ ] Cache invalidation strategy defined
- [ ] Types exported for client usage
- [ ] Server-side prefetching used where appropriate
- [ ] Loading and error states handled
- [ ] (Conditional) Optimistic updates implemented for better UX IF and ONLY if the User has requested it

Follow these patterns consistently to maintain type safety, performance, and maintainability across the application.

