---
type: "always_apply"
---

# UI Component Patterns

## Design System Rules

- Use Shadcn UI, Radix UI and Tailwind for components and styling
- Make use of components, hooks and utils from `@lilypad/ui`
- Use the `cn` function from `@lilypad/ui/lib/utils` for class names
- Avoid fixed classes like "bg-gray-500"; use semantic classes like "bg-background", "text-muted-foreground"
- Implement responsive design with mobile-first approach
- Use `useMediaQuery` hook for responsive behavior

## Component Structure Pattern

### Base Client Component Template
```typescript
'use client';

import { cn } from '@lilypad/ui/lib/utils';
import type { ComponentProps } from 'react';

export interface ComponentNameProps extends ComponentProps<'div'> {
  variant?: 'default' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

export function ComponentName({
  variant = 'default',
  size = 'md',
  className,
  children,
  ...props
}: ComponentNameProps) {
  return (
    <div
      className={cn(
        'base-classes',
        variant === 'secondary' && 'variant-classes',
        size === 'sm' && 'size-classes',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}
```

### Base Server Component Template
```typescript
import { cn } from '@lilypad/ui/lib/utils';
import type { ComponentProps } from 'react';

export interface ComponentNameProps extends ComponentProps<'div'> {
  variant?: 'default' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

export function ComponentName({
  variant = 'default',
  size = 'md',
  className,
  children,
  ...props
}: ComponentNameProps) {
  return (
    <div
      className={cn(
        'base-classes',
        variant === 'secondary' && 'variant-classes',
        size === 'sm' && 'size-classes',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}
```

### Base Page Structure
```typescript
import { Suspense } from 'react';
import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import {
  Page,
  PageBody,
  PageHeader,
  PageTitle,
} from '@lilypad/ui/components/page';
import { AppHeader } from '@/widgets/app-header/ui/app-header';
import { PAGE_METADATA } from '../metadata';

export const metadata = PAGE_METADATA;

export default function Page() {
  prefetch(trpc.router.queryFunction.queryOptions());

  return (
    <Page>
      <PageHeader>
        <AppHeader>
          <PageTitle>Page Title</PageTitle>
        </AppHeader>
      </PageHeader>
      <PageBody disableScroll>
        <HydrateClient>
          <Suspense fallback={<SkeletonComponent />}>
            <Component />
          </Suspense>
        </HydrateClient>
      </PageBody>
    </Page>
  );
}
```

## Responsive Design Patterns

### Media Query Hook Usage
```typescript
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';

// Always use MediaQueries enum for consistency
const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });
const isDesktop = useMediaQuery(MediaQueries.MdUp, { ssr: false });

// Conditional rendering pattern
{mdUp ? (
  <DesktopComponent />
) : (
  <MobileComponent />
)}
```

### Grid Layouts
```typescript
// Mobile-first responsive grid
<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
  {items.map((item) => (
    <div key={item.id} className="space-y-2">
      {/* Content */}
    </div>
  ))}
</div>
```

### Conditional Layouts
```typescript
// Show/hide elements based on screen size
<div className="flex flex-col sm:flex-row sm:items-center gap-3">
  <span className="text-muted-foreground text-xs sm:hidden">
    Mobile Label
  </span>
  <div className="hidden sm:block">
    Desktop Content
  </div>
</div>
```

### Container Patterns
```typescript
// Responsive container with proper spacing
<div className="space-y-4 sm:space-y-6">
  <div className="max-h-72 overflow-hidden sm:max-h-96">
    <ScrollArea className="h-full">
      {/* Scrollable content */}
    </ScrollArea>
  </div>
</div>
```

## Loading and Error States

### Skeleton Pattern
```typescript
import { Skeleton } from '@lilypad/ui/components/skeleton';

export function ComponentSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={`skeleton-${index}`} className="flex items-center gap-3">
          <Skeleton className="size-8 rounded-md" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-48" />
          </div>
          <Skeleton className="h-6 w-11 rounded-full" />
        </div>
      ))}
    </div>
  );
}
```

### Error Boundary Pattern
```typescript
import { Alert, AlertDescription, AlertTitle } from '@lilypad/ui/components/alert';
import { AlertCircle } from 'lucide-react';

export function ErrorState({ error }: { error: Error }) {
  return (
    <Alert variant="destructive">
      <AlertCircle className="size-4" />
      <AlertTitle>Something went wrong</AlertTitle>
      <AlertDescription>
        {error.message || 'An unexpected error occurred'}
      </AlertDescription>
    </Alert>
  );
}
```

### Conditional Rendering with If Component
```typescript
import { If } from '@lilypad/ui/components';

// Basic usage
<If condition={isLoading}>
  <LoadingSkeleton />
</If>

// With fallback
<If condition={isLoading} fallback={<Content />}>
  <LoadingSkeleton />
</If>

// With type inference
<If condition={data}>
  {(validData) => <DataDisplay data={validData} />}
</If>
```

## Interactive Components

### Button Patterns
```typescript
import { Button } from '@lilypad/ui/components/button';

// Standard button with loading state
<Button disabled={!canSubmit || isLoading} type="submit">
  {isLoading ? 'Saving...' : 'Save'}
</Button>

// Icon button with proper accessibility
<Button
  variant="ghost"
  size="icon"
  aria-label="Delete item"
  onClick={handleDelete}
>
  <TrashIcon className="size-4" />
</Button>
```

### Form Control Patterns
```typescript
import { Switch } from '@lilypad/ui/components/switch';
import { Label } from '@lilypad/ui/components/label';

// Switch with proper labeling
<div className="flex items-center space-x-2">
  <Switch
    id="setting"
    checked={isEnabled}
    onCheckedChange={setIsEnabled}
    aria-label="Toggle setting"
  />
  <Label htmlFor="setting">Enable feature</Label>
</div>
```

## Layout Patterns

### Card Layouts
```typescript
import { Card, CardContent, CardHeader, CardTitle } from '@lilypad/ui/components/card';

export function ContentCard({ title, children }: ContentCardProps) {
  return (
    <Card className="rounded-xl border border-border bg-muted/80 shadow-sm dark:bg-muted/20">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {children}
      </CardContent>
    </Card>
  );
}
```

### Master-Detail Layout
```typescript
// Responsive master-detail pattern
<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <div className="lg:col-span-1">
    {/* Master list */}
  </div>
  <div className="lg:col-span-2">
    {/* Detail view */}
  </div>
</div>
```

## Accessibility Patterns

### Focus Management
```typescript
import { useRef, useEffect } from 'react';

export function DialogContent({ isOpen }: { isOpen: boolean }) {
  const firstFocusableRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (isOpen && firstFocusableRef.current) {
      firstFocusableRef.current.focus();
    }
  }, [isOpen]);

  return (
    <div role="dialog" aria-modal="true">
      <Button ref={firstFocusableRef}>
        First focusable element
      </Button>
    </div>
  );
}
```

### ARIA Labels and Descriptions
```typescript
// Proper ARIA usage
<div
  role="group"
  aria-labelledby="settings-title"
  aria-describedby="settings-description"
>
  <h3 id="settings-title">Notification Settings</h3>
  <p id="settings-description">
    Configure how you receive notifications
  </p>
  {/* Form controls */}
</div>
```

### Keyboard Navigation
```typescript
import { useCallback } from 'react';

export function KeyboardNavigableList({ items }: ListProps) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
        // Navigate to next item
        break;
      case 'ArrowUp':
        // Navigate to previous item
        break;
      case 'Enter':
      case ' ':
        // Activate current item
        break;
    }
  }, []);

  return (
    <ul role="listbox" onKeyDown={handleKeyDown}>
      {items.map((item, index) => (
        <li
          key={item.id}
          role="option"
          tabIndex={index === 0 ? 0 : -1}
          aria-selected={item.selected}
        >
          {item.name}
        </li>
      ))}
    </ul>
  );
}
```

## Performance Patterns

### Memoization
```typescript
import { memo, useMemo } from 'react';

export const ExpensiveComponent = memo(function ExpensiveComponent({
  data,
  options,
}: ExpensiveComponentProps) {
  const processedData = useMemo(() => {
    return expensiveDataProcessing(data, options);
  }, [data, options]);

  return <div>{/* Render processed data */}</div>;
});
```

### Lazy Loading
```typescript
import { lazy, Suspense } from 'react';

const LazyComponent = lazy(() => import('./HeavyComponent'));

export function ComponentWithLazy() {
  return (
    <Suspense fallback={<ComponentSkeleton />}>
      <LazyComponent />
    </Suspense>
  );
}
```

## Animation Patterns

### Transition Classes
```typescript
// Use Tailwind transition classes
<div className="transition-colors duration-200 hover:bg-accent/30">
  Hoverable content
</div>

// Loading states with opacity
<div className={cn(
  "transition-opacity duration-200",
  isLoading ? "opacity-50" : "opacity-100"
)}>
  Content
</div>
```

### Conditional Styling
```typescript
// Dynamic classes based on state
<div className={cn(
  "rounded-lg p-3 transition-colors",
  "hover:bg-accent/50",
  isDisabled && "opacity-50 pointer-events-none",
  isActive && "bg-accent ring-2 ring-ring"
)}>
  Interactive element
</div>
```

## Dialog Patterns

### Responsive Dialog Structure
```typescript
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';

export function ResponsiveDialog({ open, onOpenChange }: DialogProps) {
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  return (
    <>
      {mdUp ? (
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent className="flex max-w-2xl flex-col gap-0 p-0">
            {/* Dialog content */}
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer open={open} onOpenChange={onOpenChange}>
          <DrawerContent className="flex flex-col">
            {/* Drawer content */}
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
}
```

### Standard Dialog Layout
```typescript
<DialogContent className="flex max-w-2xl flex-col gap-0 p-0">
  <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
    <DialogTitle className="font-semibold text-lg">Title</DialogTitle>
    <DialogDescription className="text-muted-foreground text-sm">
      Description
    </DialogDescription>
  </DialogHeader>
  
  <div className="min-h-0 flex-1 overflow-y-auto px-6 py-4">
    {/* Form content */}
  </div>
  
  <div className="flex-shrink-0 bg-secondary p-4">
    {/* Action buttons */}
  </div>
</DialogContent>