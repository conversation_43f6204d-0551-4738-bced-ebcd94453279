---
type: "always_apply"
---

# JSX Best Practices
This guide outlines our conventions for writing clean, maintainable JSX in React applications.

## Common Patterns

### Conditional Rendering with `If`

Prefer the `If` component to complex ternary operators in JSX:

```tsx
import { If } from '@lilypad/ui/components';

// Basic usage
<If condition={isLoading}>
  <Spinner />
</If>

// With fallback
<If condition={isLoading} fallback={<Content />}>
  <Spinner />
</If>

// With callback function for condition match
<If condition={user}>
  {(userData) => <UserProfile data={userData} />}
</If>
```

Benefits:
- Improves readability compared to ternary operators
- Type-safe with TypeScript
- Reduces nesting and complexity in JSX

### List Rendering

Consistently use these patterns for list rendering:

```tsx
// Empty state handling, avoid ternaries
{items.length > 0 ? (
  <ul className="list">
    {items.map((item) => (
      <li key={item.id}>{item.name}</li>
    ))}
  </ul>
) : (
  <EmptyState message="No items found" />
)}

// Even better with If component
<If condition={items.length > 0} fallback={
    <EmptyState message="No items found" />
}>
  <ul className="list">
    {items.map((item) => (
      <li key={item.id}>{item.name}</li>
    ))}
  </ul>
</If>
```

## Error and Loading States

Use consistent patterns for handling loading and error states:

```tsx
// Loading state
<If condition={isLoading}>
  <div className="flex justify-center p-8">
    <Spinner />
  </div>
</If>

// Error state that infer the type of the condition. The type of the variable "err" is now inferred
// Always use this pattern when the value of the condition is used within the body
<If condition={error}>
  {(err) => (
    <Alert variant="destructive">
      <AlertCircle className="size-4" />
      <AlertTitle>
        Alert Title
      </AlertTitle>

      <AlertDescription>
        {err.message}
      </AlertDescription>
    </Alert>
  )}
</If>

// Empty state
<If condition={items.length === 0}>
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <EmptyIcon className="size-12 text-muted-foreground" />

    <h3 className="mt-4 text-lg font-medium">
      No data found
    </h3>

    <p className="text-sm text-muted-foreground">
      No data was found.
    </p>
  </div>
</If>
```

## Responsive Conditional Rendering

Use `useMediaQuery` for responsive conditional rendering:

```tsx
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';

const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

// Responsive component rendering
{mdUp ? (
  <DesktopComponent />
) : (
  <MobileComponent />
)}

// Responsive layout
<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <div className="lg:col-span-1">
    <If condition={mdUp} fallback={<MobileSidebar />}>
      <DesktopSidebar />
    </If>
  </div>
  <div className="lg:col-span-2">
    <MainContent />
  </div>
</div>
```

## Form State Handling

Use consistent patterns for form state rendering:

```tsx
// Form submission state
<If condition={isSubmitting}>
  <Button disabled>
    <Spinner className="size-4" />
    Saving...
  </Button>
</If>

// Form success state
<If condition={isSuccess}>
  <Alert variant="default">
    <CheckCircle className="size-4" />
    <AlertDescription>
      Form submitted successfully!
    </AlertDescription>
  </Alert>
</If>

// Form error state
<If condition={error}>
  {(err) => (
    <Alert variant="destructive">
      <AlertCircle className="size-4" />
      <AlertDescription>
        {err.message}
      </AlertDescription>
    </Alert>
  )}
</If>
```

## Data Fetching States

Use consistent patterns for data fetching states:

```tsx
// Loading state
<If condition={isLoading}>
  <ComponentSkeleton />
</If>

// Error state
<If condition={error}>
  {(err) => (
    <ErrorState error={err} />
  )}
</If>

// Empty data state
<If condition={data?.length === 0}>
  <EmptyState />
</If>

// Success state with data
<If condition={data}>
  {(validData) => (
    <DataList data={validData} />
  )}
</If>
```

## Modal and Dialog States

Use consistent patterns for modal state rendering:

```tsx
// Modal open state
<If condition={isModalOpen}>
  <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
    <DialogContent>
      <DialogContent />
    </DialogContent>
  </Dialog>
</If>

// Conditional modal content
<If condition={modalType === 'edit'}>
  <EditForm />
</If>

<If condition={modalType === 'create'}>
  <CreateForm />
</If>
```

## Performance Considerations

### Memoization with Conditional Rendering

```tsx
// Memoize expensive components
const ExpensiveComponent = memo(function ExpensiveComponent({ data }) {
  return <div>{/* Expensive rendering */}</div>;
});

// Use with conditional rendering
<If condition={shouldRenderExpensive}>
  <ExpensiveComponent data={data} />
</If>
```

### Lazy Loading with Conditional Rendering

```tsx
const LazyComponent = lazy(() => import('./HeavyComponent'));

// Lazy load based on condition
<If condition={shouldLoadHeavyComponent}>
  <Suspense fallback={<ComponentSkeleton />}>
    <LazyComponent />
  </Suspense>
</If>
```

## Accessibility Patterns

### Screen Reader Support

```tsx
// Hide content from screen readers when needed
<If condition={isLoading}>
  <div aria-hidden="true">
    <Spinner />
  </div>
</If>

// Show different content for screen readers
<If condition={error}>
  {(err) => (
    <>
      <div className="sr-only">
        Error: {err.message}
      </div>
      <Alert variant="destructive">
        <AlertDescription>
          {err.message}
        </AlertDescription>
      </Alert>
    </>
  )}
</If>
```

## Best Practices

### Avoid Nested Conditionals

```tsx
// ❌ Avoid deeply nested conditionals
{isLoading ? (
  <Spinner />
) : error ? (
  <Error />
) : data ? (
  <Data />
) : (
  <Empty />
)}

// ✅ Use If component for cleaner code
<If condition={isLoading}>
  <Spinner />
</If>

<If condition={error}>
  {(err) => <Error error={err} />}
</If>

<If condition={data}>
  {(validData) => <Data data={validData} />}
</If>

<If condition={!isLoading && !error && !data}>
  <Empty />
</If>
```

### Type Safety

```tsx
// Always use type inference when possible
<If condition={user}>
  {(userData) => (
    // userData is properly typed here
    <UserProfile user={userData} />
  )}
</If>

// For arrays, ensure proper typing
<If condition={items?.length > 0}>
  {(validItems) => (
    // validItems is properly typed as non-empty array
    <ItemList items={validItems} />
  )}
</If>
```

### Consistent Patterns

```tsx
// Use consistent patterns across the app
// Loading states
<If condition={isLoading}>
  <ComponentSkeleton />
</If>

// Error states
<If condition={error}>
  {(err) => <ErrorComponent error={err} />}
</If>

// Empty states
<If condition={isEmpty}>
  <EmptyStateComponent />
</If>

// Success states
<If condition={data}>
  {(validData) => <DataComponent data={validData} />}
</If>
```

# Environment & Configuration Rules

## Environment Variables
- Always use `@t3-oss/env-nextjs` for environment validation
- Extend package-specific key schemas using the `extends` pattern
- Never access `process.env` directly in application code
- Use the centralized `env` object from `@/env`

## Package Key Management
- Each package should export a `keys.ts` file with environment schema
- Use Zod for all environment variable validation
- Group related environment variables by package domain