import { CaseRepository } from '@lilypad/db/repository/cases';
import type {
  GetCasesParams,
  GetCasesResult,
} from '@lilypad/db/repository/types/cases';
import { z } from 'zod';
import { authenticatedProcedure, createTRPCRouter } from '../core/init';
import { getCasesInputSchema } from '../schemas/cases';

export const casesRouter = createTRPCRouter({
  getCases: authenticatedProcedure
    .input(getCasesInputSchema)
    .query(async ({ ctx, input }): Promise<GetCasesResult> => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new CaseRepository(tx);
        return await repository.getCases(input as GetCasesParams);
      });
    }),

  getCaseById: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new CaseRepository(tx);
        return await repository.getCaseWithFullDetails(input);
      });
    }),

  getCasesByStudentId: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const caseRepository = new CaseRepository(tx);
        return await caseRepository.getCompleteCasesByStudentId(input);
      });
    }),

  getActiveCaseStatusByStudentId: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const caseRepository = new CaseRepository(tx);
        return await caseRepository.getActiveCaseStatusByStudentId(input);
      });
    }),
});
