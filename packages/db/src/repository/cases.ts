import type { TransactionType } from '@lilypad/db/client';
import type {
  Case<PERSON>riorityEnum,
  CaseStatusEnum,
  CaseTypeEnum,
} from '@lilypad/db/enums';
import {
  caseAssignmentsTable,
  caseDetailsTable,
  caseWorkflowStepStatusesTable,
  casesTable,
  caseWorkflowsTable,
  districtsTable,
  RoleEnum,
  rolesTable,
  schoolsTable,
  studentEnrollmentsTable,
  studentsTable,
  TaskStatusEnum,
  TaskTypeEnum,
  tasksTable,
  userDistrictsTable,
  userRolesTable,
  userSchoolsTable,
  usersTable,
  workflowStepsTable,
  workflowsTable,
} from '@lilypad/db/schema';
import type { Case, NewCase } from '@lilypad/db/schema/types';
import {
  and,
  asc,
  count,
  desc,
  eq,
  gte,
  ilike,
  inArray,
  isNotNull,
  isNull,
  lte,
  or,
  type SQL,
  sql,
} from 'drizzle-orm';
import type {
  CaseForIEPMeetingTasks,
  CaseForJoinEvaluationTasks,
  CaseForPreparationTasks,
  CaseForUpcomingEvaluations,
  CaseTableRow,
  CaseWithAssignmentsAndDetails,
  GetCasesParams,
  GetCasesResult,
} from './types/cases';
import type { WorkflowStepWithStatus } from './types/workflow-steps';

class CaseRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  /**
   * Creates a new case using admin privileges
   * @param params - The case data to create
   */
  async createCase(params: NewCase): Promise<void> {
    await this.tx.insert(casesTable).values(params);
  }

  /**
   * Creates case details (notes, metadata) for a case
   * @param caseId - The ID of the case
   * @param key - The key for the case detail
   * @param value - The value for the case detail
   */
  async createCaseDetail(
    caseId: string,
    key: string,
    value: string
  ): Promise<void> {
    await this.tx.insert(caseDetailsTable).values({
      caseId,
      key,
      value,
    });
  }

  /**
   * Gets a case by student ID to retrieve the case after creation
   * @param studentId - The ID of the student
   * @returns The first case found for the student
   */
  async getCaseByStudentId(studentId: string): Promise<{ id: string } | null> {
    const [caseRecord] = await this.tx
      .select({ id: casesTable.id })
      .from(casesTable)
      .where(
        and(
          eq(casesTable.studentId, studentId),
          eq(casesTable.isDeleted, false)
        )
      )
      .limit(1);

    return caseRecord || null;
  }

  /**
   * Gets the active case status for a student
   * @param studentId - The ID of the student
   * @returns The active case status or null if no active case exists
   */
  async getActiveCaseStatusByStudentId(
    studentId: string
  ): Promise<CaseStatusEnum | null> {
    const [caseRecord] = await this.tx
      .select({ status: casesTable.status })
      .from(casesTable)
      .where(
        and(
          eq(casesTable.studentId, studentId),
          eq(casesTable.isActive, true),
          eq(casesTable.isDeleted, false)
        )
      )
      .limit(1);

    return caseRecord?.status || null;
  }

  /**
   * Retrieves cases based on user's role and access permissions
   * Implements the same filtering logic as RLS policies but at query level for better performance
   * @param userId - The ID of the user requesting cases
   * @param userRoles - Array of role names the user has
   * @returns Array of cases the user has access to
   */
  async getCasesByUserAccess(
    userId: string,
    userRoles: string[]
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    // Super users get all cases
    if (userRoles.includes('SUPER_ADMIN')) {
      return await this.getAllCasesWithDetails(this.tx);
    }

    // Case-level access only roles (PSYCHOLOGIST, PROCTOR)
    const caseLevelOnlyRoles = ['PSYCHOLOGIST', 'PROCTOR'];
    const hasCaseLevelAccessOnly =
      userRoles.some((role) => caseLevelOnlyRoles.includes(role)) &&
      !userRoles.some((role) => !caseLevelOnlyRoles.includes(role));

    if (hasCaseLevelAccessOnly) {
      return await this.getCasesAssignedToUser(this.tx, userId);
    }

    // District-level and school-level access
    const districtLevelRoles = [
      'SPECIAL_ED_DIRECTOR',
      'CASE_MANAGER',
      'CLINICAL_DIRECTOR',
    ];
    const hasDistrictLevelAccess = userRoles.some((role) =>
      districtLevelRoles.includes(role)
    );

    if (hasDistrictLevelAccess) {
      return await this.getCasesByDistrictAccess(this.tx, userId);
    }

    // School-level access
    return await this.getCasesBySchoolAccess(this.tx, userId);
  }

  /**
   * Get all cases with details for super admin
   */
  private async getAllCasesWithDetails(
    tx: TransactionType
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await tx
      .select({
        id: casesTable.id,
        displayId: casesTable.displayId,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        primaryPsychologistId: casesTable.primaryPsychologistId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        dateOfConsent: casesTable.dateOfConsent,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .where(eq(casesTable.isDeleted, false));

    return await this.enrichCasesWithDetails(tx, cases);
  }

  /**
   * Get cases assigned to a specific user
   */
  private async getCasesAssignedToUser(
    tx: TransactionType,
    userId: string
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await tx
      .select({
        id: casesTable.id,
        displayId: casesTable.displayId,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        primaryPsychologistId: casesTable.primaryPsychologistId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        dateOfConsent: casesTable.dateOfConsent,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.userId, userId),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .where(eq(casesTable.isDeleted, false));

    return await this.enrichCasesWithDetails(tx, cases);
  }

  /**
   * Get cases by district access
   */
  private async getCasesByDistrictAccess(
    tx: TransactionType,
    userId: string
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await tx
      .select({
        id: casesTable.id,
        displayId: casesTable.displayId,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        primaryPsychologistId: casesTable.primaryPsychologistId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        dateOfConsent: casesTable.dateOfConsent,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .innerJoin(
        studentEnrollmentsTable,
        eq(studentEnrollmentsTable.studentId, casesTable.studentId)
      )
      .innerJoin(
        schoolsTable,
        eq(schoolsTable.id, studentEnrollmentsTable.schoolId)
      )
      .innerJoin(
        userDistrictsTable,
        and(
          eq(userDistrictsTable.districtId, schoolsTable.districtId),
          eq(userDistrictsTable.userId, userId)
        )
      )
      .where(eq(casesTable.isDeleted, false));

    return await this.enrichCasesWithDetails(tx, cases);
  }

  /**
   * Get cases by school access
   */
  private async getCasesBySchoolAccess(
    tx: TransactionType,
    userId: string
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await tx
      .select({
        id: casesTable.id,
        displayId: casesTable.displayId,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        primaryPsychologistId: casesTable.primaryPsychologistId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        dateOfConsent: casesTable.dateOfConsent,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .innerJoin(
        studentEnrollmentsTable,
        eq(studentEnrollmentsTable.studentId, casesTable.studentId)
      )
      .innerJoin(
        userSchoolsTable,
        and(
          eq(userSchoolsTable.schoolId, studentEnrollmentsTable.schoolId),
          eq(userSchoolsTable.userId, userId)
        )
      )
      .where(eq(casesTable.isDeleted, false));

    return await this.enrichCasesWithDetails(tx, cases);
  }

  /**
   * Enrich cases with assignments and details
   */
  private async enrichCasesWithDetails(
    tx: TransactionType,
    cases: Pick<
      Case,
      | 'id'
      | 'displayId'
      | 'status'
      | 'priority'
      | 'caseType'
      | 'studentId'
      | 'primaryPsychologistId'
      | 'isActive'
      | 'iepStatus'
      | 'iepStartDate'
      | 'iepEndDate'
      | 'dateOfConsent'
      | 'evaluationDueDate'
      | 'meetingDate'
      | 'createdAt'
      | 'updatedAt'
    >[]
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    if (cases.length === 0) {
      return [];
    }

    const caseIds = cases.map((c) => c.id);

    // Get case assignments with user details
    const caseAssignments = await tx
      .select({
        id: caseAssignmentsTable.id,
        userId: caseAssignmentsTable.userId,
        caseId: caseAssignmentsTable.caseId,
        createdAt: caseAssignmentsTable.createdAt,
        updatedAt: caseAssignmentsTable.updatedAt,
        user: {
          id: usersTable.id,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
          fullName: usersTable.fullName,
          email: usersTable.email,
          avatar: usersTable.avatar,
        },
      })
      .from(caseAssignmentsTable)
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .where(
        and(
          inArray(caseAssignmentsTable.caseId, caseIds),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      );

    // Get user roles for the assigned users
    const userIds = [...new Set(caseAssignments.map((ca) => ca.userId))];
    const userRoles =
      userIds.length > 0
        ? await tx
            .select({
              userId: userRolesTable.userId,
              role: {
                id: rolesTable.id,
                name: rolesTable.name,
              },
            })
            .from(userRolesTable)
            .innerJoin(rolesTable, eq(rolesTable.id, userRolesTable.roleId))
            .where(inArray(userRolesTable.userId, userIds))
        : [];

    // Get case details
    const caseDetails = await tx
      .select({
        id: caseDetailsTable.id,
        caseId: caseDetailsTable.caseId,
        key: caseDetailsTable.key,
        value: caseDetailsTable.value,
        createdAt: caseDetailsTable.createdAt,
        updatedAt: caseDetailsTable.updatedAt,
      })
      .from(caseDetailsTable)
      .where(
        and(
          inArray(caseDetailsTable.caseId, caseIds),
          eq(caseDetailsTable.isDeleted, false)
        )
      );

    // Group user roles by user ID
    const userRolesByUserId = userRoles.reduce(
      (acc, ur) => {
        if (!acc[ur.userId]) {
          acc[ur.userId] = [];
        }
        acc[ur.userId].push(ur.role);
        return acc;
      },
      {} as Record<string, { id: string; name: string }[]>
    );

    // Group assignments by case ID
    const assignmentsByCaseId = caseAssignments.reduce(
      (acc, ca) => {
        if (!acc[ca.caseId]) {
          acc[ca.caseId] = [];
        }
        acc[ca.caseId].push({
          ...ca,
          user: {
            ...ca.user,
            userRoles: (userRolesByUserId[ca.userId] || []).map((role) => ({
              role,
            })),
          },
        });
        return acc;
      },
      // biome-ignore lint/suspicious/noExplicitAny: Fix later
      {} as Record<string, any[]>
    );

    // Group details by case ID
    const detailsByCaseId = caseDetails.reduce(
      (acc, cd) => {
        if (!acc[cd.caseId]) {
          acc[cd.caseId] = [];
        }
        acc[cd.caseId].push(cd);
        return acc;
      },
      // biome-ignore lint/suspicious/noExplicitAny: Fix later
      {} as Record<string, any[]>
    );

    // Combine everything
    return cases.map((caseItem) => ({
      ...caseItem,
      caseAssignments: assignmentsByCaseId[caseItem.id] || [],
      caseDetails: detailsByCaseId[caseItem.id] || [],
    }));
  }

  /**
   * Retrieves all cases for a specific student with related data
   * @param studentId - The ID of the student
   * @returns Array of cases with assignments and details
   */
  async getCompleteCasesByStudentId(
    studentId: string
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await this.tx
      .select({
        id: casesTable.id,
        displayId: casesTable.displayId,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        primaryPsychologistId: casesTable.primaryPsychologistId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        dateOfConsent: casesTable.dateOfConsent,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .where(
        and(
          eq(casesTable.studentId, studentId),
          eq(casesTable.isDeleted, false)
        )
      );

    return await this.enrichCasesWithDetails(this.tx, cases);
  }

  async getCasesWithUpcomingEvaluations(
    daysAhead = 7,
    targetRoles: RoleEnum[] = [
      RoleEnum.SUPER_USER,
      RoleEnum.CASE_MANAGER,
      RoleEnum.CLINICAL_DIRECTOR,
      RoleEnum.PSYCHOLOGIST,
      RoleEnum.ASSISTANT,
    ]
  ): Promise<CaseForUpcomingEvaluations[]> {
    const now = new Date();
    const futureDate = new Date(
      now.getTime() + daysAhead * 24 * 60 * 60 * 1000
    );

    const results = await this.tx
      .select({
        caseId: casesTable.id,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        evaluationDueDate: casesTable.evaluationDueDate,
        roleName: userRolesTable.roleName,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(userRolesTable, eq(userRolesTable.userId, usersTable.id))
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.evaluationDueDate),
          gte(casesTable.evaluationDueDate, now),
          lte(casesTable.evaluationDueDate, futureDate),
          inArray(userRolesTable.roleName, targetRoles)
        )
      );

    // Filter out any results where evaluationDueDate is somehow null (extra safety)
    return results.filter(
      (result): result is typeof result & { evaluationDueDate: string } =>
        result.evaluationDueDate !== null
    );
  }

  async getCasesForPreparationTasks(
    hoursAhead = 48,
    bufferHours = 24
  ): Promise<CaseForPreparationTasks[]> {
    const now = new Date();
    const minTime = new Date(now.getTime() + bufferHours * 60 * 60 * 1000);
    const maxTime = new Date(now.getTime() + hoursAhead * 60 * 60 * 1000);

    const casesWithoutTasks = await this.tx
      .select({
        caseId: casesTable.id,
        studentId: casesTable.studentId,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        evaluationDueDate: casesTable.evaluationDueDate,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(
        userRolesTable,
        and(
          eq(userRolesTable.userId, usersTable.id),
          eq(userRolesTable.roleName, RoleEnum.PSYCHOLOGIST)
        )
      )
      .leftJoin(
        tasksTable,
        and(
          eq(tasksTable.caseId, casesTable.id),
          eq(tasksTable.assignedToId, usersTable.id),
          eq(tasksTable.taskType, TaskTypeEnum.PREPARE_FOR_EVALUATION),
          inArray(tasksTable.status, [
            TaskStatusEnum.TODO,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      )
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.evaluationDueDate),
          gte(casesTable.evaluationDueDate, minTime),
          lte(casesTable.evaluationDueDate, maxTime),
          isNull(tasksTable.id)
        )
      );

    // Filter out any results where evaluationDueDate is somehow null (extra safety)
    return casesWithoutTasks.filter(
      (result): result is typeof result & { evaluationDueDate: string } =>
        result.evaluationDueDate !== null
    );
  }

  async getCasesForJoinEvaluationTasks(
    minutesAhead = 60,
    bufferMinutes = 30
  ): Promise<CaseForJoinEvaluationTasks[]> {
    const now = new Date();
    const minTime = new Date(now.getTime() + bufferMinutes * 60 * 1000);
    const maxTime = new Date(now.getTime() + minutesAhead * 60 * 1000);

    const psychologistResults = await this.tx
      .select({
        caseId: casesTable.id,
        studentId: casesTable.studentId,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        evaluationDueDate: casesTable.evaluationDueDate,
        roleName: userRolesTable.roleName,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(
        userRolesTable,
        and(
          eq(userRolesTable.userId, usersTable.id),
          eq(userRolesTable.roleName, RoleEnum.PSYCHOLOGIST)
        )
      )
      .leftJoin(
        tasksTable,
        and(
          eq(tasksTable.caseId, casesTable.id),
          eq(tasksTable.assignedToId, usersTable.id),
          eq(tasksTable.taskType, TaskTypeEnum.JOIN_EVALUATION_AS_PSYCHOLOGIST),
          inArray(tasksTable.status, [
            TaskStatusEnum.TODO,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      )
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.evaluationDueDate),
          gte(casesTable.evaluationDueDate, minTime),
          lte(casesTable.evaluationDueDate, maxTime),
          isNull(tasksTable.id)
        )
      );

    const proctorResults = await this.tx
      .select({
        caseId: casesTable.id,
        studentId: casesTable.studentId,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        evaluationDueDate: casesTable.evaluationDueDate,
        roleName: userRolesTable.roleName,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(
        userRolesTable,
        and(
          eq(userRolesTable.userId, usersTable.id),
          eq(userRolesTable.roleName, RoleEnum.PROCTOR)
        )
      )
      .leftJoin(
        tasksTable,
        and(
          eq(tasksTable.caseId, casesTable.id),
          eq(tasksTable.assignedToId, usersTable.id),
          eq(tasksTable.taskType, TaskTypeEnum.JOIN_EVALUATION_AS_PROCTOR),
          inArray(tasksTable.status, [
            TaskStatusEnum.TODO,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      )
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.evaluationDueDate),
          gte(casesTable.evaluationDueDate, minTime),
          lte(casesTable.evaluationDueDate, maxTime),
          isNull(tasksTable.id)
        )
      );

    // Filter out null dates and add task types
    const psychologistTasks = psychologistResults
      .filter(
        (result): result is typeof result & { evaluationDueDate: string } =>
          result.evaluationDueDate !== null
      )
      .map((task) => ({
        ...task,
        taskType: TaskTypeEnum.JOIN_EVALUATION_AS_PSYCHOLOGIST,
      }));

    const proctorTasks = proctorResults
      .filter(
        (result): result is typeof result & { evaluationDueDate: string } =>
          result.evaluationDueDate !== null
      )
      .map((task) => ({
        ...task,
        taskType: TaskTypeEnum.JOIN_EVALUATION_AS_PROCTOR,
      }));

    return [...psychologistTasks, ...proctorTasks];
  }

  async getCasesForIEPMeetingTasks(
    hoursAhead = 48,
    bufferHours = 24
  ): Promise<CaseForIEPMeetingTasks[]> {
    const now = new Date();
    const minTime = new Date(now.getTime() + bufferHours * 60 * 60 * 1000);
    const maxTime = new Date(now.getTime() + hoursAhead * 60 * 60 * 1000);

    const results = await this.tx
      .select({
        caseId: casesTable.id,
        studentId: casesTable.studentId,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        meetingDate: casesTable.meetingDate,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(
        userRolesTable,
        and(
          eq(userRolesTable.userId, usersTable.id),
          eq(userRolesTable.roleName, RoleEnum.PSYCHOLOGIST)
        )
      )
      .leftJoin(
        tasksTable,
        and(
          eq(tasksTable.caseId, casesTable.id),
          eq(tasksTable.assignedToId, usersTable.id),
          eq(tasksTable.taskType, TaskTypeEnum.PREPARE_FOR_IEP_MEETING),
          inArray(tasksTable.status, [
            TaskStatusEnum.TODO,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      )
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.meetingDate),
          gte(casesTable.meetingDate, minTime),
          lte(casesTable.meetingDate, maxTime),
          isNull(tasksTable.id)
        )
      );

    // Filter out any results where meetingDate is somehow null (extra safety)
    return results.filter(
      (result): result is typeof result & { meetingDate: string } =>
        result.meetingDate !== null
    );
  }

  /**
   * Get cases with pagination, search, and filtering
   * @param params - Query parameters
   * @param user - Current user for access control
   */
  async getCases(params: GetCasesParams): Promise<GetCasesResult> {
    const {
      page = 1,
      perPage = 10,
      search,
      filters = [],
      joinOperator = 'and',
      sort = [{ id: 'createdAt', desc: true }],
    } = params;

    const baseConditions: SQL[] = [];

    // Build search conditions
    const searchCondition = this.buildSearchConditions(search);

    if (searchCondition) {
      baseConditions.push(searchCondition);
    }

    const filterConditions = this.buildFilterConditions(filters);
    baseConditions.push(...filterConditions);

    const whereClause = this.buildWhereClause(baseConditions, joinOperator);
    const orderByConditions = this.buildOrderByConditions(sort);

    const offset = (page - 1) * perPage;

    const result = await this.executeCasesQuery(
      whereClause,
      orderByConditions,
      perPage,
      offset
    );

    return {
      data: result.data,
      pagination: {
        page,
        limit: perPage,
        total: result.total,
        pages: Math.ceil(result.total / perPage),
      },
    };
  }

  /**
   * Get a single case by ID with access control
   * @param caseId - The case ID
   * @param user - Current user for access control
   */
  async getCaseById(
    caseId: string,
    userId: string
  ): Promise<CaseWithAssignmentsAndDetails | null> {
    // Use existing method to get cases with proper access control
    const userRoles: string[] = []; // Will be determined by existing access control
    const cases = await this.getCasesByUserAccess(userId, userRoles);
    const targetCase = cases.find((c) => c.id === caseId);

    return targetCase || null;
  }

  private buildSearchConditions(search: string | undefined): SQL | undefined {
    if (!search?.trim()) {
      return;
    }

    const searchTerm = search.trim();
    return sql`(
      ${studentsTable.fullName} ILIKE ${`%${searchTerm}%`} OR
      ${casesTable.id} ILIKE ${`%${searchTerm}%`} OR
      ${schoolsTable.name} ILIKE ${`%${searchTerm}%`}
    )`;
  }

  private buildFilterConditions(
    filters: Array<{ id: string; value: unknown }>
  ): SQL[] {
    const conditions: SQL[] = [];

    for (const filter of filters) {
      if (filter.value === undefined || filter.value === null) {
        continue;
      }

      switch (filter.id) {
        case 'status':
          conditions.push(
            eq(casesTable.status, filter.value as CaseStatusEnum)
          );
          break;
        case 'priority':
          conditions.push(
            eq(casesTable.priority, filter.value as CasePriorityEnum)
          );
          break;
        case 'caseType':
          conditions.push(
            eq(casesTable.caseType, filter.value as CaseTypeEnum)
          );
          break;
        case 'isActive':
          conditions.push(eq(casesTable.isActive, filter.value as boolean));
          break;
        case 'studentName':
          conditions.push(ilike(studentsTable.fullName, `%${filter.value}%`));
          break;
        default:
          break;
      }
    }

    return conditions;
  }

  private buildAccessConditions(userId: string, userRoles: string[]): SQL[] {
    const conditions: SQL[] = [eq(casesTable.isDeleted, false)];

    // Super users get all cases
    if (userRoles.includes('SUPER_ADMIN')) {
      return conditions;
    }

    // Case-level access only roles
    const caseLevelOnlyRoles = ['PSYCHOLOGIST', 'PROCTOR'];
    const hasCaseLevelAccessOnly =
      userRoles.some((role) => caseLevelOnlyRoles.includes(role)) &&
      !userRoles.some((role) => !caseLevelOnlyRoles.includes(role));

    if (hasCaseLevelAccessOnly) {
      // Only cases assigned to user
      conditions.push(sql`EXISTS (
        SELECT 1 FROM ${caseAssignmentsTable} 
        WHERE ${caseAssignmentsTable.caseId} = ${casesTable.id} 
        AND ${caseAssignmentsTable.userId} = ${userId}
        AND ${caseAssignmentsTable.isDeleted} = false
      )`);
      return conditions;
    }

    // District-level access
    const districtLevelRoles = [
      'SPECIAL_ED_DIRECTOR',
      'CASE_MANAGER',
      'CLINICAL_DIRECTOR',
    ];
    const hasDistrictLevelAccess = userRoles.some((role) =>
      districtLevelRoles.includes(role)
    );

    if (hasDistrictLevelAccess) {
      conditions.push(sql`EXISTS (
        SELECT 1 FROM ${studentEnrollmentsTable} se
        JOIN ${schoolsTable} s ON s.id = se.school_id
        JOIN ${userDistrictsTable} ud ON ud.district_id = s.district_id
        WHERE se.student_id = ${casesTable.studentId}
        AND ud.user_id = ${userId}
      )`);
      return conditions;
    }

    // School-level access
    conditions.push(sql`EXISTS (
      SELECT 1 FROM ${studentEnrollmentsTable} se
      JOIN ${userSchoolsTable} us ON us.school_id = se.school_id
      WHERE se.student_id = ${casesTable.studentId}
      AND us.user_id = ${userId}
    )`);

    return conditions;
  }

  private buildWhereClause(
    conditions: SQL[],
    joinOperator: 'and' | 'or'
  ): SQL | undefined {
    if (conditions.length === 0) {
      return;
    }

    if (conditions.length === 1) {
      return conditions[0];
    }

    return joinOperator === 'and' ? and(...conditions) : or(...conditions);
  }

  private buildOrderByConditions(
    sort: Array<{ id: string; desc: boolean }>
  ): SQL[] {
    return sort.map(({ id, desc: isDesc }) => {
      const column = this.getSortColumn(id);
      return isDesc ? desc(column) : asc(column);
    });
  }

  private getSortColumn(fieldId: string) {
    switch (fieldId) {
      case 'status':
        return casesTable.status;
      case 'priority':
        return casesTable.priority;
      case 'caseType':
        return casesTable.caseType;
      case 'studentName':
        return studentsTable.fullName;
      case 'createdAt':
        return casesTable.createdAt;
      case 'evaluationDueDate':
        return casesTable.evaluationDueDate;
      case 'meetingDate':
        return casesTable.meetingDate;
      default:
        return casesTable.createdAt;
    }
  }

  private async executeCasesQuery(
    whereClause: SQL | undefined,
    orderByConditions: SQL[],
    limit: number,
    offset: number
  ): Promise<{ data: CaseTableRow[]; total: number }> {
    // Main query with all the joins and data
    const casesQuery = this.tx
      .select({
        id: casesTable.id,
        displayId: casesTable.displayId,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        primaryPsychologistId: casesTable.primaryPsychologistId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        dateOfConsent: casesTable.dateOfConsent,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
        studentName: studentsTable.fullName,
        studentFullName: studentsTable.fullName,
        schoolName: schoolsTable.name,
        districtName: sql<string>`COALESCE(${districtsTable.name}, '')`.as(
          'districtName'
        ),
        assignedUserNames: sql<string[]>`COALESCE(
          ARRAY(
            SELECT u.full_name 
            FROM ${caseAssignmentsTable} ca 
            JOIN ${usersTable} u ON ca.user_id = u.id 
            WHERE ca.case_id = ${casesTable.id} 
            AND ca.is_deleted = false
          ), 
          ARRAY[]::text[]
        )`.as('assignedUserNames'),
        assignedUserCount: sql<number>`COALESCE(
          (SELECT COUNT(*) 
           FROM ${caseAssignmentsTable} ca 
           WHERE ca.case_id = ${casesTable.id} 
           AND ca.is_deleted = false), 
          0
        )`.as('assignedUserCount'),
      })
      .from(casesTable)
      .leftJoin(studentsTable, eq(casesTable.studentId, studentsTable.id))
      .leftJoin(
        studentEnrollmentsTable,
        eq(studentEnrollmentsTable.studentId, studentsTable.id)
      )
      .leftJoin(
        schoolsTable,
        eq(studentEnrollmentsTable.schoolId, schoolsTable.id)
      )
      .leftJoin(districtsTable, eq(schoolsTable.districtId, districtsTable.id))
      .where(whereClause)
      .orderBy(...orderByConditions)
      .limit(limit)
      .offset(offset);

    // Count query
    const countQuery = this.tx
      .select({ count: count() })
      .from(casesTable)
      .leftJoin(studentsTable, eq(casesTable.studentId, studentsTable.id))
      .leftJoin(
        studentEnrollmentsTable,
        eq(studentEnrollmentsTable.studentId, studentsTable.id)
      )
      .leftJoin(
        schoolsTable,
        eq(studentEnrollmentsTable.schoolId, schoolsTable.id)
      )
      .leftJoin(districtsTable, eq(schoolsTable.districtId, districtsTable.id))
      .where(whereClause);

    const [data, [{ count: total }]] = await Promise.all([
      casesQuery,
      countQuery,
    ]);

    return {
      data: data as CaseTableRow[],
      total,
    };
  }

  /**
   * Get comprehensive case details including workflow information
   * @param caseId - The case ID
   */
  async getCaseWithFullDetails(caseId: string) {
    // Get the full case data
    const [caseData] = await this.tx
      .select()
      .from(casesTable)
      .where(and(eq(casesTable.id, caseId), eq(casesTable.isDeleted, false)))
      .limit(1);

    if (!caseData) {
      return null;
    }

    // Get student with school information
    const studentWithSchool = await this.tx
      .select({
        student: {
          id: studentsTable.id,
          firstName: studentsTable.firstName,
          middleName: studentsTable.middleName,
          lastName: studentsTable.lastName,
          fullName: studentsTable.fullName,
          studentIdNumber: studentsTable.studentIdNumber,
          dateOfBirth: studentsTable.dateOfBirth,
          grade: studentsTable.grade,
          gender: studentsTable.gender,
        },
        school: {
          id: schoolsTable.id,
          name: schoolsTable.name,
          type: schoolsTable.type,
        },
        district: {
          id: districtsTable.id,
          name: districtsTable.name,
        },
      })
      .from(studentsTable)
      .leftJoin(
        schoolsTable,
        eq(schoolsTable.id, studentsTable.primarySchoolId)
      )
      .leftJoin(districtsTable, eq(districtsTable.id, schoolsTable.districtId))
      .where(eq(studentsTable.id, caseData.studentId))
      .limit(1);

    // Get assigned users with roles
    const assignedUsers = await this.tx
      .select({
        id: caseAssignmentsTable.id,
        user: {
          id: usersTable.id,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
          fullName: usersTable.fullName,
          email: usersTable.email,
          avatar: usersTable.avatar,
        },
        role: {
          id: rolesTable.id,
          name: rolesTable.name,
        },
        assignedAt: caseAssignmentsTable.createdAt,
      })
      .from(caseAssignmentsTable)
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(userRolesTable, eq(userRolesTable.userId, usersTable.id))
      .innerJoin(rolesTable, eq(rolesTable.id, userRolesTable.roleId))
      .where(
        and(
          eq(caseAssignmentsTable.caseId, caseId),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      );

    // Get case workflow with steps and statuses
    const caseWorkflow = await this.tx
      .select({
        id: caseWorkflowsTable.id,
        workflowId: caseWorkflowsTable.workflowId,
        status: caseWorkflowsTable.status,
        startedAt: caseWorkflowsTable.startedAt,
        completedAt: caseWorkflowsTable.completedAt,
        workflow: {
          id: workflowsTable.id,
          name: workflowsTable.name,
          description: workflowsTable.description,
          version: workflowsTable.version,
        },
      })
      .from(caseWorkflowsTable)
      .innerJoin(
        workflowsTable,
        eq(workflowsTable.id, caseWorkflowsTable.workflowId)
      )
      .where(eq(caseWorkflowsTable.caseId, caseId))
      .limit(1);

    let workflowSteps: WorkflowStepWithStatus[] = [];
    if (caseWorkflow.length > 0) {
      // Get workflow steps with their statuses
      workflowSteps = await this.tx
        .select({
          id: workflowStepsTable.id,
          stepNumber: workflowStepsTable.stepNumber,
          name: workflowStepsTable.name,
          description: workflowStepsTable.description,
          estimatedDays: workflowStepsTable.estimatedDays,
          isOptional: workflowStepsTable.isOptional,
          status: caseWorkflowStepStatusesTable.status,
          startedAt: caseWorkflowStepStatusesTable.startedAt,
          completedAt: caseWorkflowStepStatusesTable.completedAt,
          notes: caseWorkflowStepStatusesTable.notes,
        })
        .from(workflowStepsTable)
        .innerJoin(
          caseWorkflowStepStatusesTable,
          and(
            eq(caseWorkflowStepStatusesTable.stepId, workflowStepsTable.id),
            eq(caseWorkflowStepStatusesTable.caseWorkflowId, caseWorkflow[0].id)
          )
        )
        .where(eq(workflowStepsTable.workflowId, caseWorkflow[0].workflowId))
        .orderBy(workflowStepsTable.stepNumber);
    }

    return {
      ...caseData,
      student: studentWithSchool[0]?.student || null,
      school: studentWithSchool[0]?.school || null,
      district: studentWithSchool[0]?.district || null,
      assignedUsers: assignedUsers.map((au) => ({
        ...au.user,
        role: au.role,
        assignedAt: au.assignedAt,
      })),
      workflow: caseWorkflow[0]
        ? {
            ...caseWorkflow[0].workflow,
            status: caseWorkflow[0].status,
            startedAt: caseWorkflow[0].startedAt,
            completedAt: caseWorkflow[0].completedAt,
            steps: workflowSteps,
          }
        : null,
    };
  }

  /**
   * Set primary psychologist for a case
   */
  async setPrimaryPsychologist(caseId: string, psychologistId: string) {
    return await this.tx
      .update(casesTable)
      .set({
        primaryPsychologistId: psychologistId,
        updatedAt: new Date(),
      })
      .where(eq(casesTable.id, caseId))
      .returning();
  }

  /**
   * Remove primary psychologist from a case
   */
  async removePrimaryPsychologist(caseId: string) {
    return await this.tx
      .update(casesTable)
      .set({
        primaryPsychologistId: null,
        updatedAt: new Date(),
      })
      .where(eq(casesTable.id, caseId))
      .returning();
  }

  /**
   * Get primary psychologist for a case
   */
  async getPrimaryPsychologist(caseId: string) {
    const result = await this.tx
      .select({
        id: usersTable.id,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        fullName: usersTable.fullName,
        email: usersTable.email,
        avatar: usersTable.avatar,
      })
      .from(casesTable)
      .innerJoin(
        usersTable,
        eq(casesTable.primaryPsychologistId, usersTable.id)
      )
      .where(
        and(
          eq(casesTable.id, caseId),
          eq(casesTable.isDeleted, false),
          isNull(usersTable.deletedAt)
        )
      )
      .limit(1);

    return result[0] || null;
  }

  /**
   * Get all cases where a user is the primary psychologist
   */
  async getCasesByPrimaryPsychologist(
    psychologistId: string
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await this.tx
      .select({
        id: casesTable.id,
        displayId: casesTable.displayId,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        primaryPsychologistId: casesTable.primaryPsychologistId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        dateOfConsent: casesTable.dateOfConsent,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .where(
        and(
          eq(casesTable.primaryPsychologistId, psychologistId),
          eq(casesTable.isDeleted, false)
        )
      );

    return await this.enrichCasesWithDetails(this.tx, cases);
  }
}

export { CaseRepository };
