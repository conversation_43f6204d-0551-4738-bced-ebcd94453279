/** biome-ignore-all lint/performance/noNamespaceImport: Ignore */
import type { SQL } from 'drizzle-orm';
import { relations, sql } from 'drizzle-orm';
import {
  boolean,
  date,
  foreignKey,
  index,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  unique,
  uniqueIndex,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import * as Policies from '../policies/policies';
import { authUsersTable } from './auth';
import * as Enums from './enums';

/*
 * -------------------------------------------------------
 * SECTION: Tables
 * -------------------------------------------------------
 */
export const usersTable = pgTable(
  'users',
  {
    id: uuid().primaryKey(),
    firstName: varchar({ length: 255 }).notNull(),
    middleName: varchar({ length: 255 }),
    lastName: varchar({ length: 255 }).notNull(),
    email: varchar({ length: 255 }).notNull(),
    fullName: varchar({ length: 255 })
      .notNull()
      .generatedAlwaysAs(
        (): SQL =>
          sql`concat_names(${usersTable.firstName}, ${usersTable.middleName}, ${usersTable.lastName})`
      ),
    avatar: text(),
    isOnboarded: boolean().notNull().default(true),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },

  (table) => [
    foreignKey({
      columns: [table.id],
      foreignColumns: [authUsersTable.id],
    }).onDelete('cascade'),
    index('user_name_idx').on(table.fullName),
    index('user_email_idx').on(table.email),
    Policies.usersSelectPolicy,
    Policies.usersInsertPolicy,
    Policies.usersUpdatePolicy,
    Policies.usersDeletePolicy,
  ]
);

export const rolesTable = pgTable(
  'roles',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    name: Enums.roleEnum().notNull(),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [index('role_name_idx').on(table.name), Policies.rolesSelectPolicy]
);

export const permissionsTable = pgTable(
  'permissions',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    name: varchar({ length: 255 }).notNull(),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('permission_name_idx').on(table.name),
    Policies.permissionsSelectPolicy,
  ]
);

export const rolePermissionsTable = pgTable(
  'role_permissions',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    roleId: uuid()
      .notNull()
      .references(() => rolesTable.id),
    permissionId: uuid()
      .notNull()
      .references(() => permissionsTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('role_permission_role_idx').on(table.roleId),
    index('role_permission_permission_idx').on(table.permissionId),
    Policies.rolePermissionsSelectPolicy,
  ]
);

// Define addressesTable first without foreign key references to avoid circular deps
export const addressesTable = pgTable(
  'addresses',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    type: Enums.addressTypeEnum()
      .notNull()
      .default(Enums.AddressTypeEnum.PHYSICAL),
    address: varchar({ length: 255 }).notNull(),
    address2: varchar({ length: 255 }),
    city: varchar({ length: 255 }).notNull(),
    state: varchar({ length: 255 }).notNull(),
    zipcode: varchar({ length: 255 }).notNull(),
    // Polymorphic reference fields - will be constrained via foreign keys below
    studentId: uuid(),
    districtId: uuid(),
    schoolId: uuid(),
    parentId: uuid(),
    isPrimary: boolean().notNull().default(false),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('address_student_idx').on(table.studentId),
    index('address_district_idx').on(table.districtId),
    index('address_school_idx').on(table.schoolId),
    index('address_parent_idx').on(table.parentId),
    index('address_zipcode_idx').on(table.zipcode),
    index('address_type_idx').on(table.type),
    // Partial indexes for performance optimization on polymorphic queries
    index('address_student_not_null_idx')
      .on(table.studentId)
      .where(sql`student_id IS NOT NULL`),
    index('address_district_not_null_idx')
      .on(table.districtId)
      .where(sql`district_id IS NOT NULL`),
    index('address_school_not_null_idx')
      .on(table.schoolId)
      .where(sql`school_id IS NOT NULL`),
    index('address_parent_not_null_idx')
      .on(table.parentId)
      .where(sql`parent_id IS NOT NULL`),
    Policies.addressesSelectPolicy,
    Policies.addressesAllPolicy,
  ]
);

export const districtsTable = pgTable(
  'districts',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    name: varchar({ length: 255 }).notNull(),
    slug: varchar({ length: 255 }).notNull(),
    logo: text(),
    type: Enums.districtTypeEnum()
      .notNull()
      .default(Enums.DistrictTypeEnum.UNIFIED_DISTRICT),
    website: varchar({ length: 255 }).notNull(),
    ncesId: varchar({ length: 255 }).notNull(),
    stateId: varchar({ length: 255 }).notNull(),
    county: varchar({ length: 255 }).notNull(),
    numSchools: integer(),
    numStudents: integer(),
    invoiceEmail: varchar({ length: 255 }).notNull(),
    addressId: uuid()
      .notNull()
      .references(() => addressesTable.id),
  },
  () => [
    Policies.districtsSelectPolicy,
    Policies.districtsInsertPolicy,
    Policies.districtsUpdatePolicy,
    Policies.districtsDeletePolicy,
  ]
);

export const schoolsTable = pgTable(
  'schools',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    name: varchar({ length: 255 }).notNull(),
    slug: varchar({ length: 255 }).notNull(),
    type: Enums.schoolTypeEnum().notNull(),
    website: varchar({ length: 255 }),
    ncesId: varchar({ length: 255 }).notNull(),
    districtId: uuid()
      .notNull()
      .references(() => districtsTable.id),
    addressId: uuid()
      .notNull()
      .references(() => addressesTable.id),
  },
  (table) => [
    index('school_district_idx').on(table.districtId),
    Policies.schoolsSelectPolicy,
    Policies.schoolsInsertPolicy,
    Policies.schoolsUpdatePolicy,
    Policies.schoolsDeletePolicy,
  ]
);

export const studentsTable = pgTable(
  'students',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    firstName: varchar({ length: 255 }).notNull(),
    middleName: varchar({ length: 255 }),
    lastName: varchar({ length: 255 }).notNull(),
    fullName: varchar({ length: 255 }).generatedAlwaysAs(
      (): SQL =>
        sql`concat_names(${studentsTable.firstName}, ${studentsTable.middleName}, ${studentsTable.lastName})`
    ),
    preferredName: varchar({ length: 255 }),
    studentIdNumber: varchar({ length: 50 }).notNull(),
    dateOfBirth: date().notNull(),
    grade: varchar({ length: 2 }).notNull(),
    gender: Enums.genderEnum().notNull(),
    primarySchoolId: uuid().references(() => schoolsTable.id),
    privateSchool: varchar({ length: 255 }),
    enrollmentStatus: Enums.enrollmentStatusEnum()
      .notNull()
      .default(Enums.EnrollmentStatusEnum.ENROLLED),
    emergencyContactName: varchar({ length: 255 }),
    emergencyContactPhone: varchar({ length: 255 }),
    isDeleted: boolean().notNull().default(false),
    deletedAt: timestamp({ withTimezone: true }),
    deletedBy: uuid().references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('student_id_school_unique').on(
      table.studentIdNumber,
      table.primarySchoolId
    ),
    index('student_enrollment_status_idx').on(table.enrollmentStatus),
    index('student_deleted_idx').on(table.isDeleted),
    index('student_name_idx').on(table.fullName),
    Policies.studentsSelectPolicy,
    Policies.studentsInsertPolicy,
    Policies.studentsUpdatePolicy,
    Policies.studentsDeletePolicy,
  ]
);

export const userDistrictsTable = pgTable(
  'user_districts',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    districtId: uuid()
      .notNull()
      .references(() => districtsTable.id),
    userId: uuid()
      .notNull()
      .references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('user_district_unique').on(table.userId, table.districtId),
    index('user_district_user_idx').on(table.userId),
    index('user_district_district_idx').on(table.districtId),
    Policies.userDistrictsSelectPolicy,
    Policies.userDistrictsAllPolicy,
  ]
);

export const districtPreferencesTable = pgTable(
  'district_preferences',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    districtId: uuid()
      .notNull()
      .references(() => districtsTable.id, { onDelete: 'cascade' }),
    category: Enums.preferenceCategoryEnum().notNull(),
    key: varchar({ length: 255 }).notNull(),
    type: Enums.preferenceTypeEnum().notNull(),
    value: text().notNull(),
    lastModifiedBy: uuid().references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('district_preference_unique').on(table.districtId, table.key),
    index('district_pref_district_idx').on(table.districtId),
    index('district_pref_category_idx').on(table.category),
    index('district_pref_key_idx').on(table.key),
    Policies.districtPreferencesSelectPolicy,
    Policies.districtPreferencesUpdatePolicy,
  ]
);

export const userRolesTable = pgTable(
  'user_roles',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid()
      .notNull()
      .references(() => usersTable.id),
    roleId: uuid()
      .notNull()
      .references(() => rolesTable.id),
    roleName: Enums.roleEnum().notNull(),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('user_role_unique').on(table.userId, table.roleId),
    index('user_role_role_idx').on(table.roleId),
    index('user_role_user_name_idx').on(table.userId, table.roleName),
    Policies.userRolesSelectPolicy,
    Policies.userRolesAllPolicy,
  ]
);

export const userSchoolsTable = pgTable(
  'user_schools',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid().references(() => usersTable.id, {
      onDelete: 'cascade',
    }),
    schoolId: uuid().references(() => schoolsTable.id, {
      onDelete: 'cascade',
    }),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('user_school_unique').on(table.userId, table.schoolId),
    index('user_school_user_idx').on(table.userId),
    index('user_school_school_idx').on(table.schoolId),
  ]
);

export const studentEnrollmentsTable = pgTable(
  'student_enrollments',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    studentId: uuid()
      .notNull()
      .references(() => studentsTable.id, { onDelete: 'cascade' }),
    schoolId: uuid()
      .notNull()
      .references(() => schoolsTable.id, { onDelete: 'cascade' }),
    startDate: date().defaultNow(),
    endDate: date(),
    districtId: uuid().references(() => districtsTable.id, {
      onDelete: 'cascade',
    }),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('student_enrollment_student_idx').on(table.studentId),
    index('student_enrollment_school_idx').on(table.schoolId),
    index('student_enrollment_district_idx').on(table.districtId),
    uniqueIndex('student_enrollment_unique').on(
      table.studentId,
      table.schoolId
    ),
    Policies.studentEnrollmentsSelectPolicy,
    Policies.studentEnrollmentsAllPolicy,
  ]
);

export const languagesTable = pgTable(
  'languages',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    name: varchar({ length: 25 }).notNull(),
    code: varchar({ length: 10 }).notNull().unique(),
    emoji: varchar({ length: 10 }).notNull(),
  },
  (table) => [
    uniqueIndex('language_code_unique').on(table.code),
    index('language_name_idx').on(table.name),
    Policies.languagesSelectPolicy,
  ]
);

export const studentLanguagesTable = pgTable(
  'student_languages',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    isPrimary: boolean().notNull().default(false),
    studentId: uuid()
      .notNull()
      .references(() => studentsTable.id),
    languageId: uuid()
      .notNull()
      .references(() => languagesTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('student_language_unique').on(
      table.studentId,
      table.languageId
    ),
    index('student_language_primary_idx').on(table.isPrimary),
    Policies.studentLanguagesSelectPolicy,
    Policies.studentLanguagesAllPolicy,
  ]
);

export const availabilitiesTable = pgTable(
  'availabilities',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid()
      .notNull()
      .references(() => usersTable.id),
    day: Enums.dayOfWeekEnum().notNull(),
    startTime: timestamp({ withTimezone: true }).notNull(),
    endTime: timestamp({ withTimezone: true }).notNull(),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('availability_user_idx').on(table.userId),
    Policies.availabilitiesSelectPolicy,
    Policies.availabilitiesAllPolicy,
  ]
);

export const parentsTable = pgTable(
  'parents',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    firstName: varchar({ length: 255 }).notNull(),
    middleName: varchar({ length: 255 }),
    lastName: varchar({ length: 255 }).notNull(),
    fullName: varchar({ length: 255 })
      .notNull()
      .generatedAlwaysAs(
        (): SQL =>
          sql`concat_names(${parentsTable.firstName}, ${parentsTable.middleName}, ${parentsTable.lastName})`
      ),
    primaryEmail: varchar({ length: 255 }),
    secondaryEmail: varchar({ length: 255 }),
    primaryPhone: varchar({ length: 255 }),
    secondaryPhone: varchar({ length: 255 }),
    relationshipType: Enums.parentRelationshipEnum()
      .notNull()
      .default(Enums.ParentRelationshipEnum.UNKNOWN),
    isDeleted: boolean().notNull().default(false),
    deletedAt: timestamp({ withTimezone: true }),
    deletedBy: uuid().references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('parent_name_idx').on(table.fullName),
    index('parent_email_idx').on(table.primaryEmail),
    index('parent_deleted_idx').on(table.isDeleted),
    Policies.parentsSelectPolicy,
    Policies.parentsInsertPolicy,
    Policies.parentsUpdatePolicy,
    Policies.parentsDeletePolicy,
  ]
);

export const studentParentsTable = pgTable(
  'student_parents',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    parentId: uuid()
      .notNull()
      .references(() => parentsTable.id),
    studentId: uuid()
      .notNull()
      .references(() => studentsTable.id),
    isPrimaryContact: boolean().notNull().default(false),
    hasPickupAuthorization: boolean().notNull().default(true),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('student_parent_unique').on(table.studentId, table.parentId),
    index('student_parent_primary_idx').on(table.isPrimaryContact),
    Policies.studentParentsSelectPolicy,
    Policies.studentParentsAllPolicy,
  ]
);

export const casesTable = pgTable(
  'cases',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    displayId: varchar({ length: 20 })
      .notNull()
      .unique()
      .default(sql`generate_display_id('case')`),
    status: Enums.caseStatusEnum().notNull(),
    priority: Enums.casePriorityEnum()
      .notNull()
      .default(Enums.CasePriorityEnum.MEDIUM),
    caseType: Enums.caseTypeEnum().notNull(),
    studentId: uuid()
      .notNull()
      .references(() => studentsTable.id),
    primaryPsychologistId: uuid().references(() => usersTable.id, {
      onDelete: 'set null',
    }),
    isActive: boolean().notNull(),
    iepStatus: Enums.iepStatusEnum().notNull(),
    iepStartDate: timestamp({ withTimezone: true }).notNull(),
    iepEndDate: timestamp({ withTimezone: true }).notNull(),
    dateOfConsent: timestamp({ withTimezone: true }).notNull(),
    evaluationDueDate: timestamp({ withTimezone: true }),
    meetingDate: timestamp({ withTimezone: true }),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
    isDeleted: boolean().notNull().default(false),
    deletedAt: timestamp({ withTimezone: true }),
    deletedBy: uuid().references(() => usersTable.id),
  },
  (table) => [
    index('case_display_id_idx').on(table.displayId),
    index('case_priority_status_idx').on(table.priority, table.status),
    index('case_student_idx').on(table.studentId),
    index('case_type_idx').on(table.caseType),
    index('case_active_idx').on(table.isActive),
    index('case_deleted_idx').on(table.isDeleted),
    index('case_eval_due_date_idx').on(table.evaluationDueDate),
    // Composite index for RLS policy performance optimization
    index('case_student_deleted_idx').on(table.studentId, table.isDeleted),
    Policies.casesSelectPolicy,
    Policies.casesInsertPolicy,
    Policies.casesUpdatePolicy,
    Policies.casesDeletePolicy,
  ]
);

export const plansTable = pgTable(
  'plans',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    displayId: varchar({ length: 20 })
      .notNull()
      .unique()
      .default(sql`generate_display_id('plan')`),
    studentId: uuid()
      .notNull()
      .references(() => studentsTable.id),
    caseId: uuid()
      .notNull()
      .references(() => casesTable.id),
    type: Enums.planTypeEnum().notNull(),
    status: Enums.planStatusEnum().notNull(),
    expirationDate: timestamp({
      withTimezone: true,
    }).notNull(),
    isDeleted: boolean().notNull().default(false),
    deletedAt: timestamp({ withTimezone: true }),
    deletedBy: uuid().references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('plan_display_id_idx').on(table.displayId),
    index('plan_student_idx').on(table.studentId),
    index('plan_case_idx').on(table.caseId),
    index('plan_type_status_idx').on(table.type, table.status),
    index('plan_expiration_idx').on(table.expirationDate),
    index('plan_deleted_idx').on(table.isDeleted),
    Policies.plansSelectPolicy,
    Policies.plansAllPolicy,
  ]
);

export const documentsTable = pgTable(
  'documents',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    studentId: uuid()
      .notNull()
      .references(() => studentsTable.id),
    // TODO: Add foreign keys in migration after psychological testing tables are created
    // These reference tables defined in psychological-testing.ts to avoid circular imports
    assessmentSessionId: uuid(),
    testAdministrationId: uuid(),
    category: Enums.documentCategoryEnum().notNull(),
    name: varchar({ length: 255 }).notNull(),
    url: text().notNull(),
    uploadedUserId: uuid()
      .notNull()
      .references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('document_student_idx').on(table.studentId),
    index('document_category_idx').on(table.category),
    index('document_session_idx').on(table.assessmentSessionId),
    index('document_test_admin_idx').on(table.testAdministrationId),
    Policies.documentsSelectPolicy,
    Policies.documentsInsertPolicy,
    Policies.documentsUpdatePolicy,
    Policies.documentsDeletePolicy,
  ]
);

export const caseAssignmentsTable = pgTable(
  'case_assignments',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid()
      .notNull()
      .references(() => usersTable.id),
    caseId: uuid()
      .notNull()
      .references(() => casesTable.id),
    isDeleted: boolean().notNull().default(false),
    deletedAt: timestamp({ withTimezone: true }),
    deletedBy: uuid().references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('case_assignment_unique').on(table.userId, table.caseId),
    index('case_assignment_deleted_idx').on(table.isDeleted),
    index('case_assignment_user_deleted_idx').on(table.userId, table.isDeleted),
    index('case_assignment_case_idx').on(table.caseId),
    // Composite index for RLS policy performance optimization
    index('case_assignment_user_case_deleted_idx').on(
      table.userId,
      table.caseId,
      table.isDeleted
    ),
    Policies.caseAssignmentsSelectPolicy,
    Policies.caseAssignmentsInsertPolicy,
    Policies.caseAssignmentsUpdatePolicy,
    Policies.caseAssignmentsDeletePolicy,
  ]
);

export const caseDetailsTable = pgTable(
  'case_details',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    caseId: uuid()
      .notNull()
      .references(() => casesTable.id),
    key: varchar({ length: 255 }).notNull(),
    value: text().notNull(),
    isDeleted: boolean().notNull().default(false),
    deletedAt: timestamp({ withTimezone: true }),
    deletedBy: uuid().references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('case_detail_key_idx').on(table.key),
    index('case_detail_deleted_idx').on(table.isDeleted),
  ]
);

export const invitationsTable = pgTable(
  'invitations',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    roleId: uuid()
      .notNull()
      .references(() => rolesTable.id),
    districtId: uuid()
      .notNull()
      .references(() => districtsTable.id),
    inviterId: uuid()
      .notNull()
      .references(() => usersTable.id),
    firstName: varchar({ length: 255 }).notNull(),
    lastName: varchar({ length: 255 }).notNull(),
    email: varchar({ length: 255 }).notNull(),
    status: Enums.invitationStatusEnum()
      .notNull()
      .default(Enums.InvitationStatusEnum.PENDING),
    token: varchar().notNull(),
    expiresAt: timestamp({ withTimezone: true })
      .notNull()
      .default(sql`(NOW() + INTERVAL '7 days')`), // Default 7-day expiry
    acceptedAt: timestamp({ withTimezone: true }),
    rejectedAt: timestamp({ withTimezone: true }),
    invitedById: uuid().references(() => usersTable.id), // Who created this invitation (different from inviter for system invites)
    metadata: text(), // JSON field for additional invitation data
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('invitation_status_idx').on(table.status),
    index('invitation_email_idx').on(table.email),
    index('invitation_district_idx').on(table.districtId),
    index('invitation_expires_idx').on(table.expiresAt),
    index('invitation_token_idx').on(table.token),
    // Compound index for active invitations lookup
    index('invitation_active_idx').on(
      table.email,
      table.districtId,
      table.status
    ),
    Policies.invitationsSelectPolicy,
    Policies.invitationsInsertPolicy,
    Policies.invitationsUpdatePolicy,
    Policies.invitationsDeletePolicy,
  ]
);

export const invitationSchoolsTable = pgTable(
  'invitation_schools',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    invitationId: uuid()
      .notNull()
      .references(() => invitationsTable.id, { onDelete: 'cascade' }),
    schoolId: uuid()
      .notNull()
      .references(() => schoolsTable.id, { onDelete: 'cascade' }),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('invitation_school_unique').on(
      table.invitationId,
      table.schoolId
    ),
    index('invitation_schools_invitation_idx').on(table.invitationId),
    index('invitation_schools_school_idx').on(table.schoolId),
    Policies.invitationSchoolsSelectPolicy,
    Policies.invitationSchoolsAllPolicy,
  ]
);

export const tasksTable = pgTable(
  'tasks',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    taskType: Enums.taskTypeEnum('task_type').notNull(),
    caseId: uuid().references(() => casesTable.id),
    studentId: uuid().references(() => studentsTable.id),
    schoolId: uuid().references(() => schoolsTable.id),
    districtId: uuid().references(() => districtsTable.id),
    workflowStepId: uuid().references(() => workflowStepsTable.id),
    workflowStepTaskId: uuid().references(() => workflowStepTasksTable.id),
    assignedToId: uuid()
      .notNull()
      .references(() => usersTable.id, { onDelete: 'cascade' }),
    assignedById: uuid()
      .references(() => usersTable.id, { onDelete: 'set null' })
      .default(sql`null`),
    status: Enums.taskStatusEnum().notNull().default(Enums.TaskStatusEnum.TODO),
    priority: Enums.taskPriorityEnum('priority')
      .notNull()
      .default(Enums.TaskPriorityEnum.MEDIUM),
    dueDate: timestamp({ withTimezone: true }),
    completedAt: timestamp({ withTimezone: true }),
    notes: text().notNull().default(''),
    reason: text(),
    metadata: jsonb(),
    rejectedAt: timestamp({ withTimezone: true }),
    cancelledAt: timestamp({ withTimezone: true }),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('task_assigned_to_idx').on(table.assignedToId),
    index('task_status_priority_idx').on(table.status, table.priority),
    index('task_due_date_idx').on(table.dueDate),
    index('task_case_idx').on(table.caseId),
    index('task_student_idx').on(table.studentId),
    index('task_district_idx').on(table.districtId),
    index('task_type_idx').on(table.taskType),
    index('task_completed_idx').on(table.completedAt),
    index('task_assigned_status_due_idx').on(
      table.assignedToId,
      table.status,
      table.dueDate
    ),
    index('task_district_priority_idx').on(
      table.districtId,
      table.priority,
      table.createdAt
    ),
    index('task_active_idx').on(table.assignedToId, table.dueDate),
    index('task_overdue_idx').on(table.dueDate, table.status),
    index('task_workflow_step_task_idx').on(table.workflowStepTaskId),
    Policies.tasksSelectPolicy,
    Policies.tasksInsertPolicy,
    Policies.tasksUpdatePolicy,
    Policies.tasksDeletePolicy,
  ]
);

export const taskDependenciesTable = pgTable('task_dependencies', {
  id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
  predecessorTaskId: uuid()
    .notNull()
    .references(() => tasksTable.id),
  successorTaskId: uuid()
    .notNull()
    .references(() => tasksTable.id),
  createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp({ withTimezone: true })
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const taskHistoryTable = pgTable('task_history', {
  id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
  taskId: uuid()
    .notNull()
    .references(() => tasksTable.id),
  userId: uuid()
    .notNull()
    .references(() => usersTable.id),
  action: Enums.taskHistoryActionEnum().notNull(),
  previousStatus: Enums.taskStatusEnum(),
  newStatus: Enums.taskStatusEnum(),
  createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
});

export const districtAvailabilitiesTable = pgTable(
  'district_availabilities',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    districtId: uuid()
      .notNull()
      .references(() => districtsTable.id, { onDelete: 'cascade' }),
    type: Enums.availabilityTypeEnum()
      .notNull()
      .default(Enums.AvailabilityTypeEnum.EVALUATION),
    day: Enums.dayOfWeekEnum().notNull(),
    startTime: timestamp({ withTimezone: false }).notNull(), // Time only
    endTime: timestamp({ withTimezone: false }).notNull(), // Time only
    timeZone: varchar({ length: 255 }).notNull(), // e.g., "America/Los_Angeles"
    isActive: boolean().notNull().default(true),
    notes: text(),
    createdBy: uuid()
      .notNull()
      .references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('district_avail_district_idx').on(table.districtId),
    index('district_avail_day_idx').on(table.day),
    index('district_avail_type_idx').on(table.type),
    index('district_avail_active_idx').on(table.isActive),
    uniqueIndex('district_avail_unique').on(
      table.districtId,
      table.type,
      table.day,
      table.startTime,
      table.endTime
    ),
    Policies.districtAvailabilitiesSelectPolicy,
    Policies.districtAvailabilitiesUpdatePolicy,
  ]
);

export const districtBlockedDatesTable = pgTable(
  'district_blocked_dates',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    districtId: uuid()
      .notNull()
      .references(() => districtsTable.id, { onDelete: 'cascade' }),
    title: varchar({ length: 255 }).notNull(), // e.g., "Thanksgiving Holiday"
    description: text(),
    blockType: Enums.blockedDateTypeEnum()
      .notNull()
      .default(Enums.BlockedDateTypeEnum.OTHER),
    startDate: date().notNull(),
    endDate: date().notNull(), // Same as startDate for single day blocks
    isRecurring: boolean().notNull().default(false),
    recurrencePattern: text(), // JSON string for recurring rules
    isActive: boolean().notNull().default(true),
    affectsTypes: text().array(), // Array of AvailabilityTypeEnum values
    createdBy: uuid()
      .notNull()
      .references(() => usersTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('district_blocked_district_idx').on(table.districtId),
    index('district_blocked_dates_idx').on(table.startDate, table.endDate),
    index('district_blocked_type_idx').on(table.blockType),
    index('district_blocked_active_idx').on(table.isActive),
    index('district_blocked_recurring_idx').on(table.isRecurring),
    Policies.districtBlockedDatesSelectPolicy,
    Policies.districtBlockedDatesUpdatePolicy,
  ]
);

export const joinRequestsTable = pgTable(
  'join_requests',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    firstName: varchar({ length: 255 }).notNull(),
    lastName: varchar({ length: 255 }).notNull(),
    email: varchar({ length: 255 }).notNull(),
    phone: varchar({ length: 255 }).notNull(),
    districtName: varchar({ length: 255 }).notNull(),
    message: text(),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
  },
  () => [
    Policies.joinRequestsSelectPolicy,
    Policies.joinRequestsInsertPolicy,
    Policies.joinRequestsAllPolicy,
  ]
);

export const feedbackTable = pgTable(
  'feedback',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid()
      .notNull()
      .references(() => usersTable.id, { onDelete: 'cascade' }),
    rating: integer(),
    type: Enums.feedbackTypeEnum().notNull(),
    status: Enums.feedbackStatusEnum()
      .notNull()
      .default(Enums.FeedbackStatusEnum.OPEN),
    title: varchar(),
    description: text(),
    issueType: Enums.feedbackIssueTypeEnum()
      .notNull()
      .default(Enums.FeedbackIssueTypeEnum.OTHER),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('idx_feedback_rating').on(table.rating),
    index('idx_feedback_type').on(table.type),
    index('idx_feedback_status').on(table.status),
    index('idx_feedback_issue_type').on(table.issueType),
    ...Policies.feedbackPolicies,
  ]
);

export const feedbackFileTable = pgTable(
  'feedback_files',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    feedbackId: uuid()
      .notNull()
      .references(() => feedbackTable.id, { onDelete: 'cascade' }),
    fileUrl: varchar().notNull(),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  () => [...Policies.feedbackFilePolicies]
);

// biome-ignore format: Custom formatted
export const notificationsTable = pgTable(
  'notifications',
  {
		id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid().notNull().references(() => usersTable.id, { onDelete: 'cascade' }),
    type: Enums.notificationTypeEnum().notNull(),
    content: text().notNull(),
    metadata: jsonb(),
    isRead: boolean().notNull().default(false),
		isArchived: boolean().notNull().default(false),
    category: Enums.notificationCategoryTypeEnum().notNull().default(Enums.NotificationCategoryTypeEnum.GENERAL),
    readAt: timestamp({ withTimezone: true }),
		archivedAt: timestamp({ withTimezone: true }),
    expiresAt: timestamp({ withTimezone: true }).default(sql`now() + interval '1 month'`),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
  },
  (table) => [
    index('idx_notification_user_read').on(table.userId, table.isRead, table.expiresAt),
    index('idx_notification_user_archived').on(table.userId, table.isArchived, table.expiresAt),
    ...Policies.notificationPolicies,
  ]
);

// biome-ignore format: Custom formatted
export const userNotificationPreferencesTable = pgTable(
  'user_notification_preferences',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid().notNull().references(() => usersTable.id),
    notificationType: Enums.notificationTypeEnum().notNull(),
    channel: Enums.notificationChannelEnum().notNull(), // 'email' | 'in_app'
    isEnabled: boolean().notNull().default(true),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true }).notNull().defaultNow().$onUpdate(() => new Date()),
  },
  (table) => [
    unique('user_notification_type_channel_unique').on(table.userId, table.notificationType, table.channel),
    index('user_notification_preferences_user_idx').on(table.userId),
    index('user_notification_preferences_type_idx').on(table.notificationType),
    index('user_notification_preferences_channel_idx').on(table.channel),
    ... Policies.userNotificationPreferencePolicies,
  ]
);

export const workflowsTable = pgTable(
  'workflows',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    name: varchar({ length: 255 }).notNull(),
    description: text(),
    version: varchar({ length: 50 }).notNull().default('1.0'),
    isActive: boolean().notNull().default(true),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
  },
  (table) => [index('workflows_active_idx').on(table.isActive)]
);

export const workflowStepsTable = pgTable(
  'workflow_steps',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    workflowId: uuid()
      .notNull()
      .references(() => workflowsTable.id),
    stepNumber: integer().notNull(),
    name: varchar({ length: 255 }).notNull(),
    description: text(),
    estimatedDays: integer(),
    isOptional: boolean().notNull().default(false),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
  },
  (table) => [
    uniqueIndex('workflow_steps_unique').on(table.workflowId, table.stepNumber),
    index('workflow_steps_workflow_idx').on(table.workflowId),
  ]
);

export const stepDependenciesTable = pgTable(
  'workflow_step_dependencies',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    stepId: uuid()
      .notNull()
      .references(() => workflowStepsTable.id),
    dependsOnStepId: uuid()
      .notNull()
      .references(() => workflowStepsTable.id),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
  },
  (table) => [
    uniqueIndex('step_dependencies_unique').on(
      table.stepId,
      table.dependsOnStepId
    ),
    index('step_dependencies_step_idx').on(table.stepId),
  ]
);

export const workflowStepTasksTable = pgTable(
  'workflow_step_tasks',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    workflowStepId: uuid()
      .notNull()
      .references(() => workflowStepsTable.id, { onDelete: 'cascade' }),
    taskType: Enums.taskTypeEnum().notNull(),
    isRequired: boolean().notNull().default(true),
    orderIndex: integer().notNull().default(0),
    estimatedTimeline: varchar({ length: 255 }),
    autoAssignRole: Enums.roleEnum(),
    metadata: jsonb().default('{}'),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('workflow_step_tasks_step_idx').on(table.workflowStepId),
    index('workflow_step_tasks_type_idx').on(table.taskType),
    uniqueIndex('workflow_step_tasks_unique').on(
      table.workflowStepId,
      table.taskType,
      table.orderIndex
    ),
  ]
);

export const caseWorkflowsTable = pgTable(
  'case_workflows',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    caseId: uuid()
      .notNull()
      .references(() => casesTable.id),
    workflowId: uuid()
      .notNull()
      .references(() => workflowsTable.id),
    status: varchar({ length: 50 }).notNull().default('ACTIVE'),
    startedAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    completedAt: timestamp({ withTimezone: true }),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
  },
  (table) => [
    uniqueIndex('case_workflows_unique').on(table.caseId),
    index('case_workflows_status_idx').on(table.status),
  ]
);

export const caseWorkflowStepStatusesTable = pgTable(
  'case_workflow_step_statuses',
  {
    id: uuid().primaryKey().default(sql`uuid_generate_v7()`),
    caseWorkflowId: uuid()
      .notNull()
      .references(() => caseWorkflowsTable.id),
    stepId: uuid()
      .notNull()
      .references(() => workflowStepsTable.id),
    status: Enums.caseWorkflowStatusEnum()
      .notNull()
      .default(Enums.CaseWorkflowStatusEnum.PENDING),
    startedAt: timestamp({ withTimezone: true }),
    completedAt: timestamp({ withTimezone: true }),
    notes: text(),
    createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp({ withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('case_step_status_unique').on(
      table.caseWorkflowId,
      table.stepId
    ),
    index('case_step_status_workflow_idx').on(table.caseWorkflowId),
    index('case_step_status_status_idx').on(table.status),
  ]
);

/*
 * -------------------------------------------------------
 * SECTION: Relations
 * -------------------------------------------------------
 */

export const districtsRelations = relations(
  districtsTable,
  ({ many, one }) => ({
    schools: many(schoolsTable),
    userDistricts: many(userDistrictsTable),
    invitations: many(invitationsTable),
    studentEnrollments: many(studentEnrollmentsTable),
    tasks: many(tasksTable),
    preferences: many(districtPreferencesTable),
    availabilities: many(districtAvailabilitiesTable),
    blockedDates: many(districtBlockedDatesTable),
    address: one(addressesTable, {
      fields: [districtsTable.addressId],
      references: [addressesTable.id],
    }),
  })
);

export const addressesRelations = relations(
  addressesTable,
  ({ one, many }) => ({
    // Polymorphic relationships - only one should be populated per address
    student: one(studentsTable, {
      fields: [addressesTable.studentId],
      references: [studentsTable.id],
    }),
    district: one(districtsTable, {
      fields: [addressesTable.districtId],
      references: [districtsTable.id],
    }),
    school: one(schoolsTable, {
      fields: [addressesTable.schoolId],
      references: [schoolsTable.id],
    }),
    parent: one(parentsTable, {
      fields: [addressesTable.parentId],
      references: [parentsTable.id],
    }),
    // Reverse relations
    districtsUsingThisAddress: many(districtsTable),
    schoolsUsingThisAddress: many(schoolsTable),
  })
);

export const schoolsRelations = relations(schoolsTable, ({ one, many }) => ({
  district: one(districtsTable, {
    fields: [schoolsTable.districtId],
    references: [districtsTable.id],
  }),
  address: one(addressesTable, {
    fields: [schoolsTable.addressId],
    references: [addressesTable.id],
  }),
  studentEnrollments: many(studentEnrollmentsTable),
  tasks: many(tasksTable),
  userSchools: many(userSchoolsTable),
  invitationSchools: many(invitationSchoolsTable),
}));

export const studentsRelations = relations(studentsTable, ({ many }) => ({
  addresses: many(addressesTable),
  studentEnrollments: many(studentEnrollmentsTable),
  studentLanguages: many(studentLanguagesTable),
  studentParents: many(studentParentsTable),
  cases: many(casesTable),
  plans: many(plansTable),
  documents: many(documentsTable),
  tasks: many(tasksTable),
}));

export const usersRelations = relations(usersTable, ({ many }) => ({
  userDistricts: many(userDistrictsTable),
  userRoles: many(userRolesTable),
  availabilities: many(availabilitiesTable),
  caseAssignments: many(caseAssignmentsTable),
  primaryCases: many(casesTable, {
    relationName: 'case_primary_psychologist',
  }),
  documents: many(documentsTable),
  invitations: many(invitationsTable),
  tasks: many(tasksTable),
  taskHistory: many(taskHistoryTable),
  userSchools: many(userSchoolsTable),
  feedback: many(feedbackTable),
  notifications: many(notificationsTable),
  notificationPreferences: many(userNotificationPreferencesTable),
}));

export const studentEnrollmentsRelations = relations(
  studentEnrollmentsTable,
  ({ one }) => ({
    student: one(studentsTable, {
      fields: [studentEnrollmentsTable.studentId],
      references: [studentsTable.id],
    }),
    school: one(schoolsTable, {
      fields: [studentEnrollmentsTable.schoolId],
      references: [schoolsTable.id],
    }),
    district: one(districtsTable, {
      fields: [studentEnrollmentsTable.districtId],
      references: [districtsTable.id],
    }),
  })
);

export const rolesRelations = relations(rolesTable, ({ many }) => ({
  userRoles: many(userRolesTable),
  rolePermissions: many(rolePermissionsTable),
  invitations: many(invitationsTable),
}));

export const userDistrictsRelations = relations(
  userDistrictsTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [userDistrictsTable.userId],
      references: [usersTable.id],
      relationName: 'user_district_user',
    }),
    district: one(districtsTable, {
      fields: [userDistrictsTable.districtId],
      references: [districtsTable.id],
    }),
  })
);

export const districtPreferencesRelations = relations(
  districtPreferencesTable,
  ({ one }) => ({
    district: one(districtsTable, {
      fields: [districtPreferencesTable.districtId],
      references: [districtsTable.id],
    }),
    lastModifiedByUser: one(usersTable, {
      fields: [districtPreferencesTable.lastModifiedBy],
      references: [usersTable.id],
    }),
  })
);

export const userSchoolsRelations = relations(userSchoolsTable, ({ one }) => ({
  user: one(usersTable, {
    fields: [userSchoolsTable.userId],
    references: [usersTable.id],
    relationName: 'user_school_user',
  }),
  school: one(schoolsTable, {
    fields: [userSchoolsTable.schoolId],
    references: [schoolsTable.id],
  }),
}));

export const userRolesRelations = relations(userRolesTable, ({ one }) => ({
  user: one(usersTable, {
    fields: [userRolesTable.userId],
    references: [usersTable.id],
    relationName: 'user_role_user',
  }),
  role: one(rolesTable, {
    fields: [userRolesTable.roleId],
    references: [rolesTable.id],
  }),
}));

export const permissionsRelations = relations(permissionsTable, ({ many }) => ({
  rolePermissions: many(rolePermissionsTable),
}));

export const rolePermissionsRelations = relations(
  rolePermissionsTable,
  ({ one }) => ({
    role: one(rolesTable, {
      fields: [rolePermissionsTable.roleId],
      references: [rolesTable.id],
    }),
    permission: one(permissionsTable, {
      fields: [rolePermissionsTable.permissionId],
      references: [permissionsTable.id],
    }),
  })
);

export const languagesRelations = relations(languagesTable, ({ many }) => ({
  studentLanguages: many(studentLanguagesTable),
}));

export const studentLanguagesRelations = relations(
  studentLanguagesTable,
  ({ one }) => ({
    student: one(studentsTable, {
      fields: [studentLanguagesTable.studentId],
      references: [studentsTable.id],
    }),
    language: one(languagesTable, {
      fields: [studentLanguagesTable.languageId],
      references: [languagesTable.id],
    }),
  })
);

export const availabilitiesRelations = relations(
  availabilitiesTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [availabilitiesTable.userId],
      references: [usersTable.id],
      relationName: 'user_availability',
    }),
  })
);

export const parentsRelations = relations(parentsTable, ({ many }) => ({
  studentParents: many(studentParentsTable),
  addresses: many(addressesTable),
}));

export const studentParentsRelations = relations(
  studentParentsTable,
  ({ one }) => ({
    student: one(studentsTable, {
      fields: [studentParentsTable.studentId],
      references: [studentsTable.id],
    }),
    parent: one(parentsTable, {
      fields: [studentParentsTable.parentId],
      references: [parentsTable.id],
    }),
  })
);

export const casesRelations = relations(casesTable, ({ one, many }) => ({
  student: one(studentsTable, {
    fields: [casesTable.studentId],
    references: [studentsTable.id],
  }),
  primaryPsychologist: one(usersTable, {
    fields: [casesTable.primaryPsychologistId],
    references: [usersTable.id],
    relationName: 'case_primary_psychologist',
  }),
  plans: many(plansTable),
  caseAssignments: many(caseAssignmentsTable),
  caseDetails: many(caseDetailsTable),
  tasks: many(tasksTable),
  caseWorkflows: many(caseWorkflowsTable),
}));

export const plansRelations = relations(plansTable, ({ one }) => ({
  student: one(studentsTable, {
    fields: [plansTable.studentId],
    references: [studentsTable.id],
  }),
  case: one(casesTable, {
    fields: [plansTable.caseId],
    references: [casesTable.id],
  }),
}));

export const documentsRelations = relations(documentsTable, ({ one }) => ({
  student: one(studentsTable, {
    fields: [documentsTable.studentId],
    references: [studentsTable.id],
  }),
  uploadedUser: one(usersTable, {
    fields: [documentsTable.uploadedUserId],
    references: [usersTable.id],
    relationName: 'document_uploaded_by',
  }),
}));

export const caseAssignmentsRelations = relations(
  caseAssignmentsTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [caseAssignmentsTable.userId],
      references: [usersTable.id],
      relationName: 'user_case_assignment',
    }),
    case: one(casesTable, {
      fields: [caseAssignmentsTable.caseId],
      references: [casesTable.id],
    }),
  })
);

export const caseDetailsRelations = relations(caseDetailsTable, ({ one }) => ({
  case: one(casesTable, {
    fields: [caseDetailsTable.caseId],
    references: [casesTable.id],
  }),
}));

export const invitationsRelations = relations(
  invitationsTable,
  ({ one, many }) => ({
    role: one(rolesTable, {
      fields: [invitationsTable.roleId],
      references: [rolesTable.id],
    }),
    district: one(districtsTable, {
      fields: [invitationsTable.districtId],
      references: [districtsTable.id],
    }),
    inviter: one(usersTable, {
      fields: [invitationsTable.inviterId],
      references: [usersTable.id],
      relationName: 'invitation_created_by',
    }),
    invitedBy: one(usersTable, {
      fields: [invitationsTable.invitedById],
      references: [usersTable.id],
      relationName: 'invitation_invited_by',
    }),
    // Many-to-many relationship with schools
    invitationSchools: many(invitationSchoolsTable),
  })
);

export const invitationSchoolsRelations = relations(
  invitationSchoolsTable,
  ({ one }) => ({
    invitation: one(invitationsTable, {
      fields: [invitationSchoolsTable.invitationId],
      references: [invitationsTable.id],
    }),
    school: one(schoolsTable, {
      fields: [invitationSchoolsTable.schoolId],
      references: [schoolsTable.id],
    }),
  })
);

export const tasksRelations = relations(tasksTable, ({ one, many }) => ({
  case: one(casesTable, {
    fields: [tasksTable.caseId],
    references: [casesTable.id],
  }),
  student: one(studentsTable, {
    fields: [tasksTable.studentId],
    references: [studentsTable.id],
  }),
  school: one(schoolsTable, {
    fields: [tasksTable.schoolId],
    references: [schoolsTable.id],
  }),
  workflowStep: one(workflowStepsTable, {
    fields: [tasksTable.workflowStepId],
    references: [workflowStepsTable.id],
  }),
  template: one(workflowStepTasksTable, {
    fields: [tasksTable.workflowStepTaskId],
    references: [workflowStepTasksTable.id],
  }),
  district: one(districtsTable, {
    fields: [tasksTable.districtId],
    references: [districtsTable.id],
  }),
  assignedTo: one(usersTable, {
    fields: [tasksTable.assignedToId],
    references: [usersTable.id],
    relationName: 'task_assigned_to',
  }),
  assignedBy: one(usersTable, {
    fields: [tasksTable.assignedById],
    references: [usersTable.id],
    relationName: 'task_assigned_by',
  }),
  predecessorTasks: many(taskDependenciesTable, {
    relationName: 'successor_tasks',
  }),
  successorTasks: many(taskDependenciesTable, {
    relationName: 'predecessor_tasks',
  }),
  history: many(taskHistoryTable),
}));

export const taskDependenciesRelations = relations(
  taskDependenciesTable,
  ({ one }) => ({
    predecessorTask: one(tasksTable, {
      fields: [taskDependenciesTable.predecessorTaskId],
      references: [tasksTable.id],
      relationName: 'predecessor_tasks',
    }),
    successorTask: one(tasksTable, {
      fields: [taskDependenciesTable.successorTaskId],
      references: [tasksTable.id],
      relationName: 'successor_tasks',
    }),
  })
);

export const taskHistoryRelations = relations(taskHistoryTable, ({ one }) => ({
  task: one(tasksTable, {
    fields: [taskHistoryTable.taskId],
    references: [tasksTable.id],
  }),
  user: one(usersTable, {
    fields: [taskHistoryTable.userId],
    references: [usersTable.id],
    relationName: 'user_task_history',
  }),
}));

export const districtAvailabilitiesRelations = relations(
  districtAvailabilitiesTable,
  ({ one }) => ({
    district: one(districtsTable, {
      fields: [districtAvailabilitiesTable.districtId],
      references: [districtsTable.id],
    }),
    createdByUser: one(usersTable, {
      fields: [districtAvailabilitiesTable.createdBy],
      references: [usersTable.id],
      relationName: 'district_availability_created_by',
    }),
  })
);

export const districtBlockedDatesRelations = relations(
  districtBlockedDatesTable,
  ({ one }) => ({
    district: one(districtsTable, {
      fields: [districtBlockedDatesTable.districtId],
      references: [districtsTable.id],
    }),
    createdByUser: one(usersTable, {
      fields: [districtBlockedDatesTable.createdBy],
      references: [usersTable.id],
      relationName: 'district_blocked_date_created_by',
    }),
  })
);

export const feedbackFileRelations = relations(
  feedbackFileTable,
  ({ one }) => ({
    feedback: one(feedbackTable, {
      fields: [feedbackFileTable.feedbackId],
      references: [feedbackTable.id],
    }),
  })
);

export const feedbackRelations = relations(feedbackTable, ({ one, many }) => ({
  user: one(usersTable, {
    fields: [feedbackTable.userId],
    references: [usersTable.id],
  }),
  files: many(feedbackFileTable),
}));

export const workflowsRelations = relations(workflowsTable, ({ many }) => ({
  steps: many(workflowStepsTable),
  caseWorkflows: many(caseWorkflowsTable),
}));

export const workflowStepsRelations = relations(
  workflowStepsTable,
  ({ one, many }) => ({
    workflow: one(workflowsTable, {
      fields: [workflowStepsTable.workflowId],
      references: [workflowsTable.id],
    }),
    dependencies: many(stepDependenciesTable, {
      relationName: 'step_dependencies',
    }),
    dependentSteps: many(stepDependenciesTable, {
      relationName: 'depends_on_step',
    }),
    caseStepStatuses: many(caseWorkflowStepStatusesTable),
    tasks: many(tasksTable),
    taskTemplates: many(workflowStepTasksTable),
  })
);

export const workflowStepTasksRelations = relations(
  workflowStepTasksTable,
  ({ one, many }) => ({
    workflowStep: one(workflowStepsTable, {
      fields: [workflowStepTasksTable.workflowStepId],
      references: [workflowStepsTable.id],
    }),
    tasks: many(tasksTable),
  })
);

export const stepDependenciesRelations = relations(
  stepDependenciesTable,
  ({ one }) => ({
    step: one(workflowStepsTable, {
      fields: [stepDependenciesTable.stepId],
      references: [workflowStepsTable.id],
      relationName: 'step_dependencies',
    }),
    dependsOnStep: one(workflowStepsTable, {
      fields: [stepDependenciesTable.dependsOnStepId],
      references: [workflowStepsTable.id],
      relationName: 'depends_on_step',
    }),
  })
);

export const caseWorkflowsRelations = relations(
  caseWorkflowsTable,
  ({ one, many }) => ({
    case: one(casesTable, {
      fields: [caseWorkflowsTable.caseId],
      references: [casesTable.id],
    }),
    workflow: one(workflowsTable, {
      fields: [caseWorkflowsTable.workflowId],
      references: [workflowsTable.id],
    }),
    stepStatuses: many(caseWorkflowStepStatusesTable),
  })
);

export const caseStepStatusRelations = relations(
  caseWorkflowStepStatusesTable,
  ({ one }) => ({
    caseWorkflow: one(caseWorkflowsTable, {
      fields: [caseWorkflowStepStatusesTable.caseWorkflowId],
      references: [caseWorkflowsTable.id],
    }),
    step: one(workflowStepsTable, {
      fields: [caseWorkflowStepStatusesTable.stepId],
      references: [workflowStepsTable.id],
    }),
  })
);

export const notificationsRelations = relations(
  notificationsTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [notificationsTable.userId],
      references: [usersTable.id],
      relationName: 'user_notifications',
    }),
  })
);

export const userNotificationPreferencesRelations = relations(
  userNotificationPreferencesTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [userNotificationPreferencesTable.userId],
      references: [usersTable.id],
      relationName: 'user_notification_preferences',
    }),
  })
);
